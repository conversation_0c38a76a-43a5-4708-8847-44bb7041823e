package com.workplat.accept.business.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "组件运行DTO")
public class ComponentRunDTO {

    @Schema(description = "组件编码")
    private String componentCode;

    @Schema(description = "引擎编码")
    private String engineCode;

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "全局记录id")
    private String recordId;

    @Schema(description = "渲染数据")
    private Object renderData;

    @Schema(description = "提交数据")
    private Object submitData;
}
