package com.workplat.accept.business.serve.dto;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "应用dto")
public class BizServeInfoDto {
    @Schema(description = "应用id")
    private String id;
    @Schema(description = "服务名称")
    private String name;
    @Schema(description = "服务类型 数据字典:serve_type")
    private String type;
    @Schema(description = "办事指南类型  数据字典:guide_type")
    private String guideType;
    @Schema(description = "办事指南地址")
    private String guideUrl;
    @Schema(description = "是否启用")
    private String enable;
    @Schema(description = "是否第三方服务")
    private String thirdParty;
    @Schema(description = "服务方法list")
    private List<BizServeMethod> methodList;


}
