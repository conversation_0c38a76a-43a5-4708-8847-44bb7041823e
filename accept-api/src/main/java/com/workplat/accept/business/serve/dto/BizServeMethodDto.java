package com.workplat.accept.business.serve.dto;

import com.workplat.accept.business.serve.entity.BizServeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "服务方式dto")
public class BizServeMethodDto {
    @Schema(description = "服务方式id")
    private String id;
    @Schema(description = "应用实体")
    private BizServeInfo serve;
    @Schema(description = "渠道类型 数据字典:serve_method")
    private String type;
    @Schema(description = "图标id")
    private String iconId;
    @Schema(description = "渠道描述")
    private String description;
    @Schema(description = "内容")
    private String content;

}
