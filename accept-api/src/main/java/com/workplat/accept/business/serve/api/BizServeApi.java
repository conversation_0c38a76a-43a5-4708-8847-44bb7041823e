package com.workplat.accept.business.serve.api;


import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.serve.dto.BizServeInfoDto;
import com.workplat.accept.business.serve.dto.BizServeMethodDto;
import com.workplat.accept.business.serve.dto.BizServeTypeDto;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.accept.business.serve.vo.BizServeMethodVo;
import com.workplat.accept.business.serve.vo.BizServeTypeVo;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@Tag(name = "服务相关接口")
@RestController
@RequestMapping(value = "/api/serve")
public interface BizServeApi {

    @Operation(summary = "获取应用列表")
    @PostMapping("/getServeInfoList")
    ResponseData<Page<BizServeInfoVo>> queryListByPage(PageableDTO pageableDTO);

    @Operation(summary = "获取条件查询应用列表")
    @PostMapping("/queryListByName")
    ResponseData<Page<BizServeInfoVo>> queryListByName(@RequestBody JSONObject jsonObject, PageableDTO pageableDTO);

    @Operation(summary = "根据id删除应用")
    @PostMapping("/deleteById")
    ResponseData  deleteInfo(@RequestBody BizServeInfoDto BizServeInfoDto);

    @Operation(summary = "根据id修改应用")
    @PostMapping("/updateById")
    ResponseData updateById(@RequestBody BizServeInfoDto BizServeInfoDto);


    @Operation(summary = "添加应用")
    @PostMapping("/add")
    ResponseData addInfo(@RequestBody BizServeInfoDto bizServeInfoDto);

    @Operation(summary = "根据应用id获取服务列表")
    @PostMapping("/getServeMethodList")
    ResponseData<Page<BizServeMethodVo>> queryMethodListByPage(@RequestBody JSONObject jsonObject, PageableDTO pageableDTO);

    @Operation(summary = "根据应用id和keyword获取服务列表")
    @PostMapping("/getServeMethodLisByKeyword")
    ResponseData<Page<BizServeMethodVo>> queryMethodListByKeyword(@RequestBody JSONObject jsonObject, PageableDTO pageableDTO);


    @Operation(summary = "根据id删除服务")
    @PostMapping("/deleteMethodById")
    ResponseData deleteMethodById(@RequestBody BizServeMethodDto bizServeMethodDto);

    @Operation(summary = "修改服务")
    @PostMapping("/updateMethodById")
    ResponseData updateMethodById(@RequestBody BizServeMethodDto bizServeMethodDto);

    @Operation(summary = "添加应用")
    @PostMapping("/addMethod")
    ResponseData addMethod(@RequestBody BizServeMethodDto bizServeMethodDto);


    @Operation(summary = "导入数据")
    @PostMapping("/importServe")
    ResponseData importData(MultipartFile file);

    @Operation(summary = "获取认证等级字典")
    @GetMapping("/getRZDJ")
    ResponseData getRZDJ();

    @Operation(summary = "获取服务类型字典")
    @GetMapping("/getFWLX")
    ResponseData getFWLX();


    @Operation(summary = "获取办事指南类型字典")
    @GetMapping("/getBSZNLX")
    ResponseData getBSZNLX();


    @Operation(summary = "获取渠道类型字典")
    @GetMapping("/getQDLX")
    ResponseData getQDLX();

    @Operation(summary = "批量启用")
    @PostMapping("/plqy")
    ResponseData plqy(@RequestBody JSONObject jsonObject);

    @Operation(summary = "批量取消启用")
    @PostMapping("/plqxqy")
    ResponseData plqxqy(@RequestBody JSONObject jsonObject);

    @Operation(summary = "批量发布")
    @PostMapping("/plfb")
    ResponseData plfb(@RequestBody JSONObject jsonObject);

    @Operation(summary = "批量取消发布")
    @PostMapping("/plqxfb")
    ResponseData plqxfb(@RequestBody JSONObject jsonObject);

    @Operation(summary = "添加办理方式")
    @PostMapping("/addType")
    ResponseData addType(@RequestBody BizServeTypeDto bizServeTypeDto);

    @Operation(summary = "根据methodid查询办理方式列表")
    @PostMapping("/listType")
    ResponseData<Page<BizServeTypeVo>> listType(@RequestBody BizServeTypeDto bizServeTypeDto,PageableDTO pageableDTO);

    @Operation(summary = "修改办理方式")
    @PostMapping("/updateType")
    ResponseData updateType(@RequestBody BizServeTypeDto bizServeTypeDto);

    @Operation(summary = "删除办理方式")
    @PostMapping("/deleteType")
    ResponseData deleteType(@RequestBody BizServeTypeDto bizServeTypeDto);


}
