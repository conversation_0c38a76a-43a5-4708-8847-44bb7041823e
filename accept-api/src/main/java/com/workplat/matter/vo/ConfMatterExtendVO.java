package com.workplat.matter.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.vo
 * @description 事项扩展信息VO
 * @date 2025/5/21 16:17
 */
@Data
@Schema(description = "事项扩展信息VO")
public class ConfMatterExtendVO {


    @Schema(description = "事项名称")
    private String matterName;

    @Schema(description = "事项编码")
    private String matterCode;

    @Schema(description = "版本")
    private Integer version ;

    @Schema(description = "流程名称")
    private String flowName;

    @Schema(description = "流程编码")
    private String flowCode;

}
