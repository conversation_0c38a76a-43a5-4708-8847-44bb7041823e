package com.workplat.flow;


import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.support.OperatorFinder;
import com.workplat.gss.workflow.dubbo.flow.entity.GssModelInfo;
import com.workplat.gss.workflow.dubbo.flow.service.GssModelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@RestController
@RequestMapping("/flow/flowable/modelInfo")
@Tag(name = "流程信息模块")
public interface ModelInfoNodeApi {


    /**
     * 查询单个流程的用户任务
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询单个流程的用户任务节点")
    @GetMapping("/getUserTasks/{id}")
    ResponseData<?> getUserTasks(@PathVariable String id);

}

