package com.workplat.inheritance;

import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.inheritance.request.PaymentCompleteSubmitRequest;
import com.workplat.inheritance.request.SignatureInstanceRequest;
import com.workplat.inheritance.request.SignatureSubmitRequest;
import com.workplat.inheritance.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 继承公证-移动端接口
 *
 * @Author: 郏伟康
 * @Date: 2025/3/6
 * @Description:
 */
@Tag(name = "继承公证-移动端接口")
@Validated
@RequestMapping(value = "/api/inheritance/mobile")
public interface InheritanceMobileApi {

    @Operation(summary = "获得签字办件")
    @GetMapping("/getSignatureInstance")
    ResponseData<List<SignatureInstanceVO>> getSignatureInstance(@Validated SignatureInstanceRequest signatureInstanceRequest);

    @Operation(summary = "查看详情")
    @GetMapping("/getSignatureViewDetails")
    ResponseData<SignatureViewDetailsVO> getSignatureViewDetails(String instanceId);

    @Operation(summary = "查看进度")
    @GetMapping("/getSignatureViewProgress")
    ResponseData<SignatureViewProgressVO> getSignatureViewProgress(String instanceId);

    @Operation(summary = "签字详情")
    @GetMapping("/getSignatureDetails")
    ResponseData<SignatureDetailsVO> getSignatureDetails(String instanceId);

    @Operation(summary = "签字提交")
    @PostMapping("/signatureSubmit")
    public ResponseData<Void> signatureSubmit(@RequestBody SignatureSubmitRequest submitRequest) throws Exception;

    @Operation(summary = "签字详情保存")
    @PostMapping("/signatureDetailsSubmit")
    ResponseData<Void> SignatureDetailsSubmit(String instanceId);

    @Operation(summary = "缴费详情")
    @GetMapping("/getPaymentDetails")
    ResponseData<PaymentDetailsVO> getPaymentDetails(String instanceId);

    @Operation(summary = "缴费完成提交")
    @PostMapping("/paymentCompleteSubmit")
    ResponseData<Void> PaymentCompleteSubmit(@RequestBody PaymentCompleteSubmitRequest request);

    @Operation(summary = "退回重新提交")
    @PostMapping("/returnToReSubmit")
    ResponseData<Void> returnToReSubmit(String instanceId);


}
