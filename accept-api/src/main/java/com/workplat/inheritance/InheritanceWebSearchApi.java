package com.workplat.inheritance;

import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.inheritance.query.BizInstanceInheritQuery;
import com.workplat.inheritance.vo.BizInstanceInheritVO;
import com.workplat.inheritance.vo.FileVO;
import com.workplat.inheritance.vo.InheritanceSearchDetailsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @Author: 郏伟康
 * @Date: 2025/3/8
 * @Description:
 */
@Tag(name = "继承公证业务查询-web端接口")
@Validated
@RequestMapping(value = "/api/inheritance/web/search")
public interface InheritanceWebSearchApi {

    @Operation(summary = "业务查询分页")
    @GetMapping("/page")
    ResponseData<Page<BizInstanceInheritVO>> page(BizInstanceInheritQuery query, PageableDTO pageable);

    @Operation(summary = "详情")
    @GetMapping("/details")
    ResponseData<InheritanceSearchDetailsVO> details(String instanceId);

    @Operation(summary = "历程")
    @GetMapping("/process")
    ResponseData<List<BizInstanceWorkFlowVO>> process(String instanceId);

    @Operation(summary = "查看文件")
    @GetMapping("/viewFile")
    ResponseData<List<FileVO>> viewFile(String instanceId);


}
