package com.workplat.inheritance;

import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.inheritance.request.*;
import com.workplat.inheritance.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: 郏伟康
 * @Date: 2025/2/21
 * @Description:
 */
@Tag(name = "继承公证-web端接口")
@Validated
@RequestMapping(value = "/api/inheritance/web")
public interface InheritanceWebApi {

    @Operation(summary = "不动产登记簿查询")
    @GetMapping("/realEstateQuery")
    ResponseData<RealEstateVO> realEstateQuery(@Validated RealEstateRequest request) throws Exception;

    @Operation(summary = "发起继承公证/开始办理（生成新的instanceId，使用最新的事项发布配置）")
    @PostMapping("/startHandlingInheritance")
    ResponseData<String> startHandlingInheritance(@Validated RealEstateRequest request) throws Exception;

    @Operation(summary = "获得主页表单")
    @GetMapping("/getHomePageFrom")
    ResponseData<HomePageFromVO> getHomePageFrom(String instanceId);

    @Operation(summary = "主页表单提交")
    @PostMapping("/homePageFromSubmit")
    ResponseData<String> homePageFromSubmit(@RequestBody HomePageFromSubmitRequest homePageFromSubmitRequest);

    @Operation(summary = "获得继承人信息填写表单")
    @GetMapping("getHeirInfoFillForm")
    ResponseData<HeirInfoFillFormVO> getHeirInfoFillForm(String instanceId);

    @Operation(summary = "继承人信息填写表单提交")
    @PostMapping("/heirInfoFillFormSubmit")
    ResponseData<String> heirInfoFillFormSubmit(@RequestBody HeirInfoFillFormSubmitRequest heirInfoFillFormSubmitSubmitRequest) throws Exception;

    @Operation(summary = "预览新的产权人信息")
    @PostMapping("previewNewPropertyOwnerInfo")
    ResponseData<PreviewNewPropertyOwnerInfoVO> previewNewPropertyOwnerInfo(@RequestBody PreviewNewPropertyOwnerInfoRequest previewNewPropertyOwnerInfoRequest);

    @Operation(summary = "获得继承人信息补充界面")
    @GetMapping("/getHeirInfoSupplementFrom")
    ResponseData<HeirInfoSupplementVO> getHeirInfoSupplementFrom(String instanceId);

    @Operation(summary = "重新签名")
    @PostMapping("/reSignature")
    ResponseData<Void> reSignature(String instanceId, String idNumber) throws Exception;

    @Operation(summary = "签字提交")
    @PostMapping("/signatureSubmit")
    ResponseData<Void> signatureSubmit(@RequestBody SignatureSubmitRequest submitRequest) throws Exception;

    @Operation(summary = "继承人信息补充界面提交")
    @PostMapping("/heirInfoSupplementFromSubmit")
    ResponseData<String> heirInfoSupplementFromSubmit(@RequestBody HeirInfoSupplementSubmitRequest supplementSubmitRequest);

    @Operation(summary = "发起流程")
    @PostMapping("/startProcessSubmit")
    ResponseData<Void> startProcessSubmit(@RequestBody StartProcessSubmitRequest startProcessSubmitRequest);


}
