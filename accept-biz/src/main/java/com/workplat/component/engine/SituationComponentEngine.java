package com.workplat.component.engine;

import com.workplat.component.dto.ComponentDataContext;
import com.workplat.gss.service.item.api.matter.api.accept.ConfMatterAcceptSituationApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 情境组件引擎
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Service
public class SituationComponentEngine extends AbstractComponentEngine {

    @Autowired
    ConfMatterAcceptSituationApi confMatterAcceptSituationApi;

    @Override
    public Object load(ComponentDataContext componentDataContext) {

        return confMatterAcceptSituationApi.getById(componentDataContext.getAcceptId());
    }

    @Override
    public Object process(ComponentDataContext componentDataContext) {
        String input = componentDataContext.getInput();
        // 获取组件信息
        return null;
    }

    @Override
    public String name() {
        return "情境组件引擎";
    }

    @Override
    public String code() {
        return "situation";
    }
}
