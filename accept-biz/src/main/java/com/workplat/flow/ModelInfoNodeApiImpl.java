package com.workplat.flow;

import com.workplat.flow.vo.NodeTaskVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.workflow.dubbo.flow.service.GssModelInfoService;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.UserTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
public class ModelInfoNodeApiImpl implements ModelInfoNodeApi {

    @Autowired
    private GssModelInfoService modelInfoService;


    @Override
    public ResponseData<?> getUserTasks(String id) {
        List<NodeTaskVO> list = new ArrayList<>();
        BpmnModel bpmnModel = this.modelInfoService.getBpmnModelById(id);
        List<UserTask> userTasks = bpmnModel.getMainProcess().findFlowElementsOfType(UserTask.class);
        for (UserTask userTask : userTasks) {
           NodeTaskVO nodeTaskVO = new NodeTaskVO();
           nodeTaskVO.setId(userTask.getId());
           nodeTaskVO.setName(userTask.getName());
           list.add(nodeTaskVO);
        }
        return ResponseData.success(list);
    }
}
