package com.workplat.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.workplat.componentEngine.engine.content.InstructionConstant;

import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 表单字段筛选工具类测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class FormFieldFilterUtilTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        try {
            // 读取测试数据
            String formPath = "accept-biz/src/main/java/com/workplat/utils/form.json";
            String fieldMapPath = "accept-biz/src/main/java/com/workplat/utils/fieldMap.json";
            String fieldsFilterMapPath = "accept-biz/src/main/java/com/workplat/utils/FieldsFilterMap.json";

            String formMetadata = new String(Files.readAllBytes(Paths.get(formPath)));
            String fieldMapData = new String(Files.readAllBytes(Paths.get(fieldMapPath)));
            String fieldsFilterMapData = new String(Files.readAllBytes(Paths.get(fieldsFilterMapPath)));

            System.out.println("=== 原始表单元数据 ===");
            printFormStructure(formMetadata);

            System.out.println("\n=== 字段映射数据 ===");
            System.out.println(fieldMapData);

//            // 测试功能一：筛选保留
//            System.out.println("\n=== 功能一：筛选保留指定字段 ===");
//            String filteredForm = FormFieldFilterUtil.filterFormByFieldMap(formMetadata, fieldMapData);
//            printFormStructure(filteredForm);
//
//            // 测试功能二：移除指定字段
//            System.out.println("\n=== 功能二：移除指定字段 ===");
//            String removedForm = FormFieldFilterUtil.removeFormFieldsByMap(formMetadata, fieldMapData);
//            printFormStructure(removedForm);

            // 测试新功能：带索引追踪的筛选（保留模式）
//            System.out.println("\n=== 新功能：带索引追踪的筛选（保留模式）===");
//            FormFieldFilterUtil.FormFilterResult keepResult = FormFieldFilterUtil.filterFormWithIndexTracking(formMetadata, fieldMapData, true);
//            System.out.println("处理结果：" + keepResult);
//            printFormStructure(keepResult.getFilteredFormJson());
//            boolean b = FormStepProcessor.hasFieldsToFill(keepResult.getFilteredFormJson());
//            System.out.println("表单是否完成：" + b);

//            // 测试新功能：带索引追踪的筛选（移除模式）
//            System.out.println("\n=== 新功能：带索引追踪的筛选（移除模式）===");
//            FormFieldFilterUtil.FormFilterResult removeResult = FormFieldFilterUtil.filterFormWithIndexTracking(formMetadata, fieldMapData, false);
//            System.out.println("处理结果：" + removeResult);
//            printFormStructure(removeResult.getFilteredFormJson());


            try {
                // 获取表单字段过滤结果
                FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                         formMetadata,
                        fieldMapData,
                        true // 保留模式
                );
                // 判断是否还有字段可以填写
                if (!FormStepProcessor.hasFieldsToFill(filterResult.getFilteredFormJson(), fieldsFilterMapData)) {
                    System.out.printf("%s", InstructionConstant.SUCCESS_NEXT.getCode());
                }else {
                    System.out.printf("%s", InstructionConstant.KEEP_AT_PRESENT.getCode());
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 打印表单结构概要
     */
    private static void printFormStructure(String formJson) throws Exception {
        JsonNode formNode = objectMapper.readTree(formJson);
        JsonNode renderList = formNode.get("renderList");
        
        if (renderList != null && renderList.isArray()) {
            for (int i = 0; i < renderList.size(); i++) {
                JsonNode item = renderList.get(i);
                printFormItem(item, 0);
            }
        }
    }
    
    /**
     * 递归打印表单项
     */
    private static void printFormItem(JsonNode item, int level) {
        if (item == null) return;
        
        String indent = "  ".repeat(level);
        String name = item.has("name") ? item.get("name").asText() : "未知";
        String type = item.has("type") ? item.get("type").asText() : "";
        String componentName = item.has("componentName") ? item.get("componentName").asText() : "";
        
        String field = "";
        JsonNode propsNode = item.get("props");
        if (propsNode != null && propsNode.has("field")) {
            field = " [field: " + propsNode.get("field").asText() + "]";
        }
        
        System.out.println(indent + "- " + name + " (" + componentName + ")" + 
                          (type.isEmpty() ? "" : " [type: " + type + "]") + field);
        
        // 递归打印子项
        JsonNode childNode = item.get("child");
        if (childNode != null && childNode.isArray()) {
            for (JsonNode child : childNode) {
                printFormItem(child, level + 1);
            }
        }
    }
}
