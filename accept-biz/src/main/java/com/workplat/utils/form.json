{"tableName": "", "renderList": [{"id": "CnAMXBRsLJ", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "eGZHUHGIi", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "a8203bc1e6b34ede998ef87e9fbea126", "key": "ddrxm", "field": "ddrxm", "label": "调动人姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputddrxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsddrxm", "name": "foucs事件", "value": ""}, {"key": "changeddrxm", "name": "change事件", "value": ""}, {"key": "clickddrxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "调动人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "svhptia2b", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "c632613466064442bfa1db61cb3e2ab4", "key": "ddrsfzhm", "field": "ddrsfzhm", "label": "调动人身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputddrsfzhm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsddrsfzhm", "name": "foucs事件", "value": ""}, {"key": "changeddrsfzhm", "name": "change事件", "value": ""}, {"key": "clickddrsfzhm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "调动人身份证号码不能为空", "trigger": "blur", "required": true}, {"message": "请输入正确的身份证号码", "pattern": "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[xX])$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "g3CGwteJH", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "4e1883f173204014848b38b5b895932a", "key": "lxdh", "field": "lxdh", "label": "联系电话", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputlxdh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucslxdh", "name": "foucs事件", "value": ""}, {"key": "changelxdh", "name": "change事件", "value": ""}, {"key": "clicklxdh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "联系电话不能为空", "trigger": "blur", "required": true}, {"message": "请输入正确的联系电话", "pattern": "^((0\\d{2,3}-\\d{7,8})|(1[3456789]\\d{9}))$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "RSxRVxUgq", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "7543108570b34a4b8e6cd2b1cb312ffb", "key": "qcdz", "field": "qcdz", "label": "迁出地址", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputqcdz", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsqcdz", "name": "foucs事件", "value": ""}, {"key": "changeqcdz", "name": "change事件", "value": ""}, {"key": "clickqcdz", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "迁出地址不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "7HgtE3r_F", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "a0fe0d6cbafb4f33bd118d382adc4d58", "key": "qcdhkdjjg", "field": "qcdhkdjjg", "label": "迁出地户口登记机关", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputqcdhkdjjg", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsqcdhkdjjg", "name": "foucs事件", "value": ""}, {"key": "changeqcdhkdjjg", "name": "change事件", "value": ""}, {"key": "clickqcdhkdjjg", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "迁出地户口登记机关不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "zzvNz7Ggl", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "f0280848c675492687d8ea65504d9532", "key": "qrdssxfj", "field": "qrdssxfj", "label": "迁入地所属县分局", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputqrdssxfj", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsqrdssxfj", "name": "foucs事件", "value": ""}, {"key": "changeqrdssxfj", "name": "change事件", "value": ""}, {"key": "clickqrdssxfj", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "迁入地所属县分局不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "bhozkJpRN", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "5fee430b958843c6a1ddd480e209240c", "key": "qrdz", "field": "qrdz", "label": "迁入地址", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputqrdz", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsqrdz", "name": "foucs事件", "value": ""}, {"key": "changeqrdz", "name": "change事件", "value": ""}, {"key": "clickqrdz", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "迁入地址不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "H3CKBXYz2", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "81f2ab0b1c154c2eb809ff57d2f526ea", "key": "qrdsspcs", "field": "qrdsspcs", "label": "迁入地所属派出所", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputqrdsspcs", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsqrdsspcs", "name": "foucs事件", "value": ""}, {"key": "changeqrdsspcs", "name": "change事件", "value": ""}, {"key": "clickqrdsspcs", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "迁入地所属派出所不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "iYgKPYC4D", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "a6ebe3fe15434556aba8e59ca4689d25", "key": "lhlx", "field": "lhlx", "label": "落户类型", "options": [{"label": "人才落户", "value": "人才落户", "disabled": false}, {"label": "毕业生落户", "value": "毕业生落户", "disabled": false}], "disabled": false, "functions": [{"key": "changelhlx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "落户类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}], "props": {"show": true, "field": "ASmGqGcwm", "title": "申请人信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "y7bMqABFAF", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "dgHYfi_3N", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "c26ac3847e9747d8ae3171f6b625b09a", "key": "sqry", "field": "sqry", "label": "随迁人员姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputsqry", "name": "input事件", "value": "console.log(form)"}, {"key": "foucssqry", "name": "foucs事件", "value": ""}, {"key": "changesqry", "name": "change事件", "value": ""}, {"key": "clicksqry", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "5oSbAFHZq", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "e69b55bc07994127a20bfccb1d6759fe", "key": "ysqrgx", "field": "ysqrgx", "label": "与申请人关系", "options": [{"label": "子女", "value": "子女", "disabled": false}, {"label": "配偶", "value": "配偶", "disabled": false}], "disabled": false, "functions": [{"key": "changeysqrgx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "VcSpNhaTp", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "2d9fd0fd2a644270869acb970bd0380e", "key": "sqrysfzhm", "field": "sqrysfzhm", "label": "随迁人员身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputsqry<PERSON><PERSON><PERSON><PERSON>", "name": "input事件", "value": "console.log(form)"}, {"key": "foucssqrysfzhm", "name": "foucs事件", "value": ""}, {"key": "changesqrysfzhm", "name": "change事件", "value": ""}, {"key": "clicksqrysfzhm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "请输入正确的身份证号码", "pattern": "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[xX])$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "J1FPjiCz5", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "c11238b1ffe849f0b8336ef6db2bde43", "key": "sqryqchjdz", "field": "sqryqchjdz", "label": "随迁人员迁出户籍地址", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputsqryqchjdz", "name": "input事件", "value": "console.log(form)"}, {"key": "foucssqryqchjdz", "name": "foucs事件", "value": ""}, {"key": "changesqryqchjdz", "name": "change事件", "value": ""}, {"key": "clicksqryqchjdz", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "lEq_4qpR8", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "8a279bbd368145ea8849d2938279315a", "key": "sqryqchksspcs", "field": "sqryqchksspcs", "label": "随迁人员迁出户口所属派出所", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputsqryqchksspcs", "name": "input事件", "value": "console.log(form)"}, {"key": "foucssqryqchksspcs", "name": "foucs事件", "value": ""}, {"key": "changesqryqchksspcs", "name": "change事件", "value": ""}, {"key": "clicksqryqchksspcs", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "sqrylb_table", "title": "随迁人员", "addText": "新增", "functions": [{"key": "deletexzHUEb6Fp", "name": "delete事件", "value": ""}, {"key": "addxzHUEb6Fp", "name": "add事件", "value": ""}], "isNeedMax": false, "isNeedMin": false, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": null, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}, {"id": "9MD-pY2tH7", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "iXVPho0uW-", "icon": "icon-kaiguan3", "name": "邮寄地址", "type": "addressArea", "child": [{"id": "Tuw2zFwDKO", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"field": "zjlqfs", "label": "请选证件证领取方式", "options": [{"label": "线下自取", "value": "线下自取", "disabled": false}, {"label": "邮寄（到付）", "value": "邮寄（到付）", "disabled": false}], "disabled": false, "showById": "", "functions": [{"key": "changeU-HQO138H", "name": "change事件", "value": ""}], "optionKey": "", "isNeedHide": false, "labelWidth": "", "modelValue": "线下自取", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "showByValue": "", "labelDescribe": ""}, "rules": [{"message": "选择器不能为空", "trigger": "blurf0u0XhARXpayment", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "6FOEg4oqTD", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "", "field": "sjrxm", "label": "收件人姓名", "default": "", "disabled": false, "readonly": false, "showById": "zjlqfs", "clearable": false, "functions": [{"key": "inputf0u0XhARXname", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsf0u0XhARXname", "name": "foucs事件", "value": ""}, {"key": "changef0u0XhARXname", "name": "change事件", "value": ""}, {"key": "clickf0u0XhARXname", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "showByValue": "邮寄（到付）", "labelDescribe": "", "listInterface": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "titleButtonText": "", "isDesensitization": ""}, "rules": [{"message": "输入框不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-button-title"}, {"id": "fqGBEbP739", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "", "field": "sjrsjhm", "label": "收件人手机号码", "default": "", "disabled": false, "readonly": false, "showById": "zjlqfs", "clearable": false, "functions": [{"key": "inputf0u0XhARXmobile", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsf0u0XhARXmobile", "name": "foucs事件", "value": ""}, {"key": "changef0u0XhARXmobile", "name": "change事件", "value": ""}, {"key": "clickf0u0XhARXmobile", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "showByValue": "邮寄（到付）", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "输入框不能为空", "trigger": "blur", "required": true}, {"message": "", "pattern": "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", "trigger": "blur"}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input-number"}, {"id": "aFh8v-6QYC", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "", "field": "sjdz", "label": "收件地址", "default": "", "disabled": false, "readonly": false, "showById": "zjlqfs", "clearable": false, "functions": [{"key": "inputf0u0XhARXaddress", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsf0u0XhARXaddress", "name": "foucs事件", "value": ""}, {"key": "changef0u0XhARXaddress", "name": "change事件", "value": ""}, {"key": "clickf0u0XhARXaddress", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "showByValue": "邮寄（到付）", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "输入框不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-detail-address"}], "props": {"show": true, "field": "f0u0XhARX", "isHidden": false, "functions": [], "listInterface": "", "saveInterface": ""}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetMailingAddress"}], "props": {"show": true, "field": "yjdz", "title": "邮寄地址", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true, "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "PjrdI8r972", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "tgJXNDJ6U", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "ac5f12ee4159499c8888e9e90be18df2", "key": "xhzxm", "field": "xhzxm", "label": "新户主姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputxhzxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsxhzxm", "name": "foucs事件", "value": ""}, {"key": "changexhzxm", "name": "change事件", "value": ""}, {"key": "clickxhzxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "非必填", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "RxjryMrAS", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "ea2a19f3e5ea45c7a863c3c949937e48", "key": "xhzsfzmh", "field": "xhzsfzmh", "label": "新户主身份证号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputxhzsfzmh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsxhzsfzmh", "name": "foucs事件", "value": ""}, {"key": "changexhzsfzmh", "name": "change事件", "value": ""}, {"key": "clickxhzsfzmh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "非必填", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}], "props": {"show": true, "field": "dXxto3UaO", "title": "如原户主迁出， 请填写以下信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "demoFormName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "{}", "submitFunction": "{}"}