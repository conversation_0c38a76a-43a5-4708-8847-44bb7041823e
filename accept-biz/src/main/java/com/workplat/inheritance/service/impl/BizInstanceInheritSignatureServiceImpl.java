package com.workplat.inheritance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.core.util.BeanUtils;
import com.workplat.gss.file.constant.SysFileSource;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.inheritance.converter.BizInstanceInheritSignatureVOConvert;
import com.workplat.inheritance.entity.BizInstanceInheritSignature;
import com.workplat.inheritance.service.BizInstanceInheritSignatureService;
import com.workplat.inheritance.vo.AbandonDengBoInfoVO;
import com.workplat.inheritance.vo.NewPropertyOwnerInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 继承公证服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class BizInstanceInheritSignatureServiceImpl extends BaseServiceImpl<BizInstanceInheritSignature> implements BizInstanceInheritSignatureService {

    @Autowired
    private BizInstanceInheritSignatureVOConvert bizInstanceInheritSignatureVOConvert;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private SysFileEntityService sysFileEntityService;

    @Override
    public List<BizInstanceInheritSignature> getSignatureInstanceByZzqzrsfzhm(String zzqzrsfzhm) {
//        List<BizInstanceInheritSignature> bizInstanceInheritSignatures = this.queryForList(ImmutableMap.of(
//                "=(zzqzrsfzhm)", zzqzrsfzhm)
//        );
        List<BizInstanceInheritSignature> bizInstanceInheritSignatures = this.queryForList(new HashMap<>());
        return bizInstanceInheritSignatures;
    }

    @Override
    public List<BizInstanceInheritSignature> getBizInstanceInheritSignatureList(String instanceId) {
        return this.queryForList(ImmutableMap.of("=(instance.id)", instanceId));
    }

    @Override
    public void generateSignatureInfo(String instanceId, List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList, List<AbandonDengBoInfoVO> abandonDengBoInfoVOList) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        // 获得老的签字列表
        List<BizInstanceInheritSignature> inheritSignatureList = getBizInstanceInheritSignatureList(instanceId);
        Map<String, BizInstanceInheritSignature> inheritSignatureMap = inheritSignatureList.stream()
                .collect(Collectors.toMap(k -> k.getSfzhm(), v -> v, (v1, v2) -> v1));
        // 删除老的签字列表
        this.deleteByParams(ImmutableMap.of("=(instance.id)", instanceId));
        // 增加新的签字列表
        for (NewPropertyOwnerInfoVO newPropertyOwnerInfoVO : newPropertyOwnerInfoVOList) {
            BizInstanceInheritSignature bizInstanceInheritSignature = new BizInstanceInheritSignature();
            BeanUtils.copyProperties(newPropertyOwnerInfoVO, bizInstanceInheritSignature);
            bizInstanceInheritSignature.setInstance(bizInstanceInfo);
            bizInstanceInheritSignature.setSfsfqdbr(false);
            bizInstanceInheritSignature.setZzqzrsfzhm("有".equals(bizInstanceInheritSignature.getSfydlr())
                    ? bizInstanceInheritSignature.getDlrsfzh() : bizInstanceInheritSignature.getSfzhm());
            if (inheritSignatureMap.containsKey(bizInstanceInheritSignature.getSfzhm())) {
                BizInstanceInheritSignature inheritSignatureOld = inheritSignatureMap.get(bizInstanceInheritSignature.getSfzhm());
                bizInstanceInheritSignature.setSfwcqz(inheritSignatureOld.isSfwcqz());
                bizInstanceInheritSignature.setSignatureFileId(inheritSignatureOld.getSignatureFileId());
            }
            this.save(bizInstanceInheritSignature);
        }
        for (AbandonDengBoInfoVO abandonDengBoInfoVO : abandonDengBoInfoVOList) {
            BizInstanceInheritSignature bizInstanceInheritSignature = new BizInstanceInheritSignature();
            BeanUtils.copyProperties(abandonDengBoInfoVO, bizInstanceInheritSignature);
            bizInstanceInheritSignature.setInstance(bizInstanceInfo);
            bizInstanceInheritSignature.setSfsfqdbr(true);
            bizInstanceInheritSignature.setZzqzrsfzhm("有".equals(bizInstanceInheritSignature.getSfydlr())
                    ? bizInstanceInheritSignature.getDlrsfzh() : bizInstanceInheritSignature.getSfzhm());
            if (inheritSignatureMap.containsKey(bizInstanceInheritSignature.getSfzhm())) {
                BizInstanceInheritSignature inheritSignatureOld = inheritSignatureMap.get(bizInstanceInheritSignature.getSfzhm());
                bizInstanceInheritSignature.setSfwcqz(inheritSignatureOld.isSfwcqz());
                bizInstanceInheritSignature.setSignatureFileId(inheritSignatureOld.getSignatureFileId());
            }
            this.save(bizInstanceInheritSignature);
        }

    }

    @Override
    public void setSignatureSituation(String instanceId, List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList, List<AbandonDengBoInfoVO> abandonDengBoInfoVOList) {
        List<BizInstanceInheritSignature> bizInstanceInheritSignatureList = getBizInstanceInheritSignatureList(instanceId);
        Map<String, BizInstanceInheritSignature> signatureMap = bizInstanceInheritSignatureList.stream().collect(Collectors.toMap(v -> v.getSfzhm(), k -> k, (v1, v2) -> v1));
        // 新产权人
        for (NewPropertyOwnerInfoVO newPropertyOwnerInfoVO : newPropertyOwnerInfoVOList) {
            if (signatureMap.containsKey(newPropertyOwnerInfoVO.getSfzhm())) {
                BizInstanceInheritSignature bizInstanceInheritSignature = signatureMap.get(newPropertyOwnerInfoVO.getSfzhm());
                newPropertyOwnerInfoVO.setSfwcqz(bizInstanceInheritSignature.isSfwcqz());
                newPropertyOwnerInfoVO.setSfzxqz(bizInstanceInheritSignature.isSfzxqz());
                newPropertyOwnerInfoVO.setSignatureFileId(bizInstanceInheritSignature.getSignatureFileId());

            }
        }
        if (CollUtil.isEmpty(abandonDengBoInfoVOList)) {
            return;
        }
        // 放弃登薄人
        for (AbandonDengBoInfoVO abandonDengBoInfoVO : abandonDengBoInfoVOList) {
            if (signatureMap.containsKey(abandonDengBoInfoVO.getSfzhm())) {
                BizInstanceInheritSignature bizInstanceInheritSignature = signatureMap.get(abandonDengBoInfoVO.getSfzhm());
                abandonDengBoInfoVO.setSfwcqz(bizInstanceInheritSignature.isSfwcqz());
                abandonDengBoInfoVO.setSfzxqz(bizInstanceInheritSignature.isSfzxqz());
                abandonDengBoInfoVO.setSignatureFileId(bizInstanceInheritSignature.getSignatureFileId());
            }
        }

    }

    @Override
    public void reSignature(String instanceId, String idNumber) {
        List<BizInstanceInheritSignature> inheritSignatureList = this.queryForList(
                ImmutableMap.of("=(instance.id)", instanceId, "=(sfzhm)", idNumber));

        for (BizInstanceInheritSignature inheritSignature : inheritSignatureList) {
            inheritSignature.setSignatureFileId("");
            inheritSignature.setSfwcqz(false);
            this.update(inheritSignature);
        }
    }

    @Override
    public void signatureSubmit(String instanceId, String idNumber, String file) throws Exception {
        List<BizInstanceInheritSignature> inheritSignatureList = this.queryForList(
                ImmutableMap.of("=(instance.id)", instanceId, "=(sfzhm)", idNumber));
        if (inheritSignatureList.isEmpty()) {
            return;
        }
        BizInstanceInheritSignature bizInstanceInheritSignature = inheritSignatureList.get(0);
        // 图片格式
        if (!file.startsWith("data:image")) {
            throw new BusinessException("图片格式不正确");
        }
        String contentType = file.split(";")[0].split(":")[1];// image/png
        String suffix = contentType.split("/")[1]; // png
        file = file.split(";")[1].split(",")[1];
        // 上传文件
        byte[] decodedBytes = Base64.getDecoder().decode(file);
        SysFileUploadModel uploadModel = new SysFileUploadModel();
        uploadModel.setContent(new ByteArrayInputStream(decodedBytes));
        uploadModel.setFileName(bizInstanceInheritSignature.getXm().concat("签字图片.").concat(suffix));
        uploadModel.setContentType(contentType);
        uploadModel.setLength(Long.valueOf(decodedBytes.length));
        uploadModel.setFileSource(SysFileSource.SYSTEM_USER_PUBLIC_FILE.name());
        uploadModel.setMd5(DigestUtils.md5Hex(decodedBytes));
        SysFileEntity sysFileEntity = sysFileEntityService.upload(uploadModel);
        for (BizInstanceInheritSignature inheritSignature : inheritSignatureList) {
            inheritSignature.setSignatureFileId(sysFileEntity.getId());
            inheritSignature.setSfwcqz(true);
            this.update(inheritSignature);
        }
    }


}
