package com.workplat.inheritance.service.impl;

import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceWorkFlowService;
import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import com.workplat.inheritance.constant.InheritanceStagePageEnum;
import com.workplat.inheritance.converter.BizInstanceInheritVOConvert;
import com.workplat.inheritance.entity.BizInstanceInherit;
import com.workplat.inheritance.service.BizInstanceInheritService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 继承公证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class BizInstanceInheritServiceImpl extends BaseServiceImpl<BizInstanceInherit> implements BizInstanceInheritService {

    @Autowired
    private BizInstanceInheritVOConvert bizInstanceInheritVOConvert;
    @Autowired
    private BizInstanceWorkFlowService bizInstanceWorkFlowService;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;

    @Override
    public BizInstanceInherit getByInstanceId(String instanceId) {
        BizInstanceInherit bizInstanceInherit = this.queryForSingle(ImmutableMap.of("=(instance.id)", instanceId));
        return bizInstanceInherit;
    }

    @Override
    public void setInheritanceCurrentNode(String instanceId, InheritanceNodeStatusEnum inheritanceNodeStatus
            , String reviewOpinion) {
        BizInstanceInherit bizInstanceInherit = getByInstanceId(instanceId);
        bizInstanceInherit.setCurrentNode(inheritanceNodeStatus.name());
        bizInstanceInherit.setCurrentNodeName(inheritanceNodeStatus.getValue());
        // 插入业务流程
        bizInstanceWorkFlowService.addBizInstanceWorkFlow(instanceId, inheritanceNodeStatus.name()
                , inheritanceNodeStatus.getValue()
                , "空"
                , StringUtils.isEmpty(reviewOpinion) ? inheritanceNodeStatus.getValue() : reviewOpinion);
        this.update(bizInstanceInherit);
    }

    @Override
    public void setInheritanceStagePage(String instanceId, InheritanceStagePageEnum inheritanceStagePage) {
        BizInstanceInherit bizInstanceInherit = getByInstanceId(instanceId);
        bizInstanceInherit.setStagePage(inheritanceStagePage.name());
        this.update(bizInstanceInherit);

    }

    @Override
    public List<BizInstanceWorkFlowVO> getBizInstanceWorkFlowVOList(String instanceId) {
        List<BizInstanceWorkFlowVO> workFlowVOList = null;
        workFlowVOList = bizInstanceWorkFlowService.getBizInstanceWorkFlowVoList(instanceId);
        // 模拟流程
//        workFlowVOList = new ArrayList<>();
//        BizInstanceWorkFlowVO bizInstanceWorkFlowVO = null;
//        for (int i = 0; i < 5; i++) {
//            bizInstanceWorkFlowVO = new BizInstanceWorkFlowVO();
//            bizInstanceWorkFlowVO.setInstanceId(instanceId);
//            bizInstanceWorkFlowVO.setNo(i);
//            bizInstanceWorkFlowVO.setCode("测试编码" + i);
//            bizInstanceWorkFlowVO.setTitle("测试标题" + i);
//            bizInstanceWorkFlowVO.setPassed(true);
//            bizInstanceWorkFlowVO.setCreateTime(new Date());
//            List<BizInstanceWorkFlowOpinionVO> workFlowOpinionVOList = new ArrayList<>();
//            BizInstanceWorkFlowOpinionVO bizInstanceWorkFlowOpinionVO = new BizInstanceWorkFlowOpinionVO();
//            bizInstanceWorkFlowOpinionVO.setReviewOpinion("测试意见" + i);
//            workFlowOpinionVOList.add(bizInstanceWorkFlowOpinionVO);
//            bizInstanceWorkFlowVO.setWorkFlowOpinionVOList(workFlowOpinionVOList);
//            workFlowVOList.add(bizInstanceWorkFlowVO);
//        }
        return workFlowVOList;
    }

}
