package com.workplat.inheritance.converter;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.inheritance.entity.BizInstanceInherit;
import com.workplat.inheritance.vo.BizInstanceInheritVO;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8
 */
@Component
public class BizInstanceInheritVOConvert implements BaseConverter<BizInstanceInherit, BizInstanceInheritVO> {

    @Override
    public BizInstanceInheritVO convert(BizInstanceInherit source) {
        BizInstanceInheritVO target = BaseConverter.super.convert(source);
        if (source.getInstance() != null) {
            target.setInstanceId(source.getInstance().getId());
            target.setAcceptedNum(source.getInstance().getAcceptedNum());
        }
        return target;
    }
}
