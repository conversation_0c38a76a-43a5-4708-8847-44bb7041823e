package com.workplat.inheritance.converter;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.inheritance.entity.BizInstanceInheritSignature;
import com.workplat.inheritance.vo.BizInstanceInheritSignatureVO;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Component
public class BizInstanceInheritSignatureVOConvert implements BaseConverter<BizInstanceInheritSignature, BizInstanceInheritSignatureVO> {

    @Override
    public BizInstanceInheritSignatureVO convert(BizInstanceInheritSignature source) {
        BizInstanceInheritSignatureVO target = BaseConverter.super.convert(source);
        return target;
    }
}
