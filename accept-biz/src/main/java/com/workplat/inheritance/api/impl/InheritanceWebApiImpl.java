package com.workplat.inheritance.api.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.deepoove.poi.config.Configure;
import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterial;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterialFile;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialFileService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.util.DateTimeUtils;
import com.workplat.gss.common.doc.word.WordRenderUtils;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.gss.material.dubbo.entity.ConfMaterialFile;
import com.workplat.gss.material.dubbo.entity.ConfMaterials;
import com.workplat.gss.material.dubbo.service.ConfMaterialsService;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.gss.service.item.dubbo.matter.vo.accept.ConfMatterAcceptFormVO;
import com.workplat.inheritance.InheritanceWebApi;
import com.workplat.inheritance.constant.*;
import com.workplat.inheritance.dto.HeirInfoFillFormDataDTO;
import com.workplat.inheritance.dto.HomePageFromDataDTO;
import com.workplat.inheritance.dto.RealEstateDTO;
import com.workplat.inheritance.entity.BizInstanceInherit;
import com.workplat.inheritance.request.*;
import com.workplat.inheritance.service.BizInstanceInheritService;
import com.workplat.inheritance.service.BizInstanceInheritSignatureService;
import com.workplat.inheritance.vo.*;
import com.workplat.utils.FractionOrPercentageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/21 14:52
 */
@Slf4j
@Transactional(rollbackFor = Exception.class)
@RestController
public class InheritanceWebApiImpl implements InheritanceWebApi {

    /**
     * 不动产登记簿查询url
     */
    @Value("${inheritance.realEstateQueryUrl:}")
    private String realEstateQueryUrl;

    /**
     * 受理编号前缀
     */
    public static final String ACCEPTED_NUM_PREFIX = "TC";

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ConfMaterialsService confMaterialsService;
    @Autowired
    private SysFileEntityService sysFileEntityService;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private ConfMatterService confMatterService;
    @Autowired
    private BizInstanceFieldsService bizInstanceFieldsService;
    @Autowired
    private BizInstanceInheritSignatureService bizInstanceInheritSignatureService;
    @Autowired
    private BizInstanceInheritService bizInstanceInheritService;
    @Autowired
    private BizInstanceMaterialService bizInstanceMaterialService;
    @Autowired
    private BizInstanceMaterialFileService bizInstanceMaterialFileService;


    @ApiLogging(module = "继承公证-web端接口", operation = "不动产登记簿查询", type = OperationType.QUERY)
    @Override
    public ResponseData<RealEstateVO> realEstateQuery(RealEstateRequest request) throws Exception {
        // 调不动产查询接口
        RealEstateDTO realEstateDTO = realRealEstateQuery(request);

        // 转换
        RealEstateVO realEstateVO = convertingEntities(realEstateDTO);

        // 生成不动产登记簿记录文件
        String fileUrl = generateRealEstateRegisterFile(realEstateDTO);
        realEstateVO.setFileId(fileUrl);
        realEstateVO.setQueryDate(DateTimeUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss"));
        return ResponseData.success(realEstateVO);
    }

    @Override
    public ResponseData<String> startHandlingInheritance(RealEstateRequest request) throws Exception {
        // 调不动产查询接口
        RealEstateDTO realEstateDTO = realRealEstateQuery(request);
        RealEstateDTO.Qllist qllist = realEstateDTO.getData().getQllist().get(0);
        // 以下两种情况可以办理：1）不动产不存在查封情况 2）不动产不存在抵押情况，或者仅有一次抵押且银行同意带押过户的
        if (StringUtils.isNotEmpty(qllist.getXzzt())) {
            if (qllist.getXzzt().contains("查封")) {
                throw new BusinessException("存在查封情况,不可办理！");
            } else if (qllist.getXzzt().contains("抵押") && qllist.getDyaqxxlist().size() > 1) {
                throw new BusinessException("存在抵押情况,不可办理！");
            }
        }

        // 初始化instance,生成新的instanceId，使用最新的事项发布配置
        ConfMatter confMatter = confMatterService.getByCode(ConfMatterCodeEnum.MAIN.getCode());
        BizInstanceInfoVO instanceInfoVO = bizInstanceInfoService.initialize(confMatter.getId(), ACCEPTED_NUM_PREFIX);
        // 不动产信息
        RealEstateVO realEstateVO = convertingEntities(realEstateDTO);
        // 保存不动产信息到instance
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceInfoVO.getId());
        JSONObject jsonObject = new JSONObject();
//        jsonObject.put(BizInstanceExtendJsonEnum.REAL_ESTATE_QUERY.getCode(), request);
        jsonObject.put(BizInstanceExtendJsonEnum.REAL_ESTATE_DTO.getCode(), realEstateDTO);
        jsonObject.put(BizInstanceExtendJsonEnum.REAL_ESTATE_VO.getCode(), realEstateVO);
        bizInstanceInfo.extendJsonAdd(jsonObject);
        // 设置基础信息
        BizInstanceInherit bizInstanceInherit = new BizInstanceInherit();
        bizInstanceInherit.setInstance(bizInstanceInfo);
        bizInstanceInherit.setYcqrxm(getYcqrxm(realEstateDTO));
        bizInstanceInherit.setZl(realEstateVO.getFwzl());
        bizInstanceInherit.setStagePage(InheritanceStagePageEnum.HOME_PAGE.name());
        bizInstanceInheritService.save(bizInstanceInherit);
        // 设置当前业务当前节点
        bizInstanceInheritService.setInheritanceCurrentNode(bizInstanceInfo.getId(), InheritanceNodeStatusEnum.PRE_ACCEPTANCE
                , "暂存");
        return ResponseData.success(bizInstanceInfo.getId());
    }

    @Override
    public ResponseData<HomePageFromVO> getHomePageFrom(String instanceId) {
        HomePageFromVO homePageFromVO = new HomePageFromVO();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);

        // 拿到主页不动产信息
        Object o = bizInstanceInfo.extendJsonGet(BizInstanceExtendJsonEnum.REAL_ESTATE_VO.getCode());
        RealEstateVO realEstateVO = JSON.parseObject(String.valueOf(o), RealEstateVO.class);

        // 拿到主页表单formJson
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryForSingleByInstanceId(instanceId);
        Map<String, Object> formJsonMap = bizInstanceFieldsVO.getFormJsonMap();
        if (formJsonMap == null || formJsonMap.get(ConfMatterCodeEnum.HOME_PAGE.getCode()) == null) {
            throw new BusinessException("表单没有配置");
        }
        String from = String.valueOf(formJsonMap.get(ConfMatterCodeEnum.HOME_PAGE.getCode()));
        ConfMatterAcceptFormVO matterAcceptFormVO = JSON.parseObject(from, ConfMatterAcceptFormVO.class);
        String formJson = matterAcceptFormVO.getFormJson();

        // 查询主页是否已经提交过表单数据
        BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
        if (instanceFields != null
                && instanceFields.getFormJsonMap() != null
                && instanceFields.getFormJsonMap().containsKey(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode())) {
            Object object = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode());
            JSONObject jsonObject = JSONObject.from(object);
            homePageFromVO.setInstanceId(instanceId);
            homePageFromVO.setFormData(jsonObject.getString("formData"));
            homePageFromVO.setFormJson(formJson);
            homePageFromVO.setRealEstateVO(realEstateVO);
            return ResponseData.success(homePageFromVO);
        }

        // 主页第一次进入
        // 填充表单数据formData（原产权人）
        Object oo = bizInstanceInfo.extendJsonGet(BizInstanceExtendJsonEnum.REAL_ESTATE_DTO.getCode());
        RealEstateDTO estateDTO = JSON.parseObject(String.valueOf(oo), RealEstateDTO.class);
        HomePageFromDataDTO homePageFromDataDTO = fillOriginalOwnerFormData(estateDTO);
        String formData = JSON.toJSONString(homePageFromDataDTO, JSONWriter.Feature.NullAsDefaultValue);

        // 包装返回
        homePageFromVO.setInstanceId(instanceId);
        homePageFromVO.setFormJson(formJson);
        homePageFromVO.setFormData(formData);
        homePageFromVO.setRealEstateVO(realEstateVO);
        return ResponseData.success(homePageFromVO);
    }


    @Override
    public ResponseData<String> homePageFromSubmit(HomePageFromSubmitRequest homePageFromSubmitRequest) {
        String instanceId = homePageFromSubmitRequest.getInstanceId();
        String formData = homePageFromSubmitRequest.getFormData();
        // 保存表单数据
        BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
        Map<String, Object> formJsonMap = instanceFields.getFormJsonMap();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("formData", formData);
        formJsonMap.put(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode(), jsonObject);
        instanceFields.setFormJsonMap(formJsonMap);
        bizInstanceFieldsService.update(instanceFields);
        // 设置申报暂存当前停留界面
        bizInstanceInheritService.setInheritanceStagePage(instanceId, InheritanceStagePageEnum.HOME_PAGE);
        return ResponseData.success(instanceId);
    }

    @Override
    public ResponseData<HeirInfoFillFormVO> getHeirInfoFillForm(String instanceId) {
        HeirInfoFillFormVO heirInfoFillFormVO = new HeirInfoFillFormVO();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);

        String formJson = ""; // 表单
        String formData = ""; // 表单数据

        // 拿到继承人信息表单formJson
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryForSingleByInstanceId(instanceId);
        Map<String, Object> formJsonMap = bizInstanceFieldsVO.getFormJsonMap();
        if (formJsonMap == null || formJsonMap.get(ConfMatterCodeEnum.HEIR_INFO_FILL_IN.getCode()) == null) {
            throw new BusinessException("表单没有配置");
        }
        String from = String.valueOf(formJsonMap.get(ConfMatterCodeEnum.HEIR_INFO_FILL_IN.getCode()));
        ConfMatterAcceptFormVO matterAcceptFormVO = JSON.parseObject(from, ConfMatterAcceptFormVO.class);
        formJson = matterAcceptFormVO.getFormJson();

        // 查询继承人信息是否已经提交过表单数据(一般退回上一步主页再下一步会出现这种情况)
        BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
        if (instanceFields != null
                && instanceFields.getFormJsonMap() != null
                && instanceFields.getFormJsonMap().containsKey(BizInstanceFieldsFormJsonMapEnum.HEIR_INFO_FILL_FROM_JSON.getCode())) {
            Object o = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HEIR_INFO_FILL_FROM_JSON.getCode());
            formData = JSONObject.from(o).getString("formData");
        }

        // 填充被继承人表单formData数据
        // 拿到主页原产权人formData信息
        String prePageFormData = "";
        Object o = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode());
        JSONObject jsonObject = JSONObject.from(o);
        prePageFormData = jsonObject.getString("formData");
        // 根据继承人信息是否存在走不同逻辑（一般退回上一步主页再下一步会出现这种情况或者暂存件再次进入）
        HeirInfoFillFormDataDTO heirInfoFillFormDataDTO = null;
        if (StringUtils.isEmpty(formData)) {
            heirInfoFillFormDataDTO = beHeirInfoAnalysis(prePageFormData, null);
        } else {
            heirInfoFillFormDataDTO = beHeirInfoAnalysis(prePageFormData, formData);
        }
        formData = JSON.toJSONString(heirInfoFillFormDataDTO, JSONWriter.Feature.NullAsDefaultValue);

        // 包装返回
        heirInfoFillFormVO.setInstanceId(instanceId);
        heirInfoFillFormVO.setFormJson(formJson);
        heirInfoFillFormVO.setFormData(formData);
        heirInfoFillFormVO.setHomePageFormData(prePageFormData);
        return ResponseData.success(heirInfoFillFormVO);
    }

    @Override
    public ResponseData<String> heirInfoFillFormSubmit(HeirInfoFillFormSubmitRequest heirInfoFillFormSubmitSubmitRequest) throws Exception {
        String instanceId = heirInfoFillFormSubmitSubmitRequest.getInstanceId();
        String formData = heirInfoFillFormSubmitSubmitRequest.getFormData();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        // 保存表单数据
        BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
        Map<String, Object> formJsonMap = instanceFields.getFormJsonMap();
        formJsonMap.put(BizInstanceFieldsFormJsonMapEnum.HEIR_INFO_FILL_FROM_JSON.getCode()
                , JSONObject.of("formData", formData));
        instanceFields.setFormJsonMap(formJsonMap);
        bizInstanceFieldsService.update(instanceFields);

        // 保存原产权人信息,签名文件预览,新产权人信息,放弃登薄人信息到instanceExtendJson
        JSONObject jsonObject = saveInstanceExtendJson(bizInstanceInfo, instanceFields);
        bizInstanceInfo.extendJsonAdd(jsonObject);
        bizInstanceInfoService.update(bizInstanceInfo);

        // 生成签名信息
        bizInstanceInheritSignatureService.generateSignatureInfo(instanceId
                , jsonObject.getList(BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.getCode(), NewPropertyOwnerInfoVO.class)
                , jsonObject.getList(BizInstanceExtendJsonEnum.ABANDON_DENG_BO_INFO_VO.getCode(), AbandonDengBoInfoVO.class));

        // 设置申报暂存当前停留界面
        bizInstanceInheritService.setInheritanceStagePage(instanceId, InheritanceStagePageEnum.HEIR_FILLIN_PAGE);
        return ResponseData.success(instanceId);
    }

    @Override
    public ResponseData<PreviewNewPropertyOwnerInfoVO> previewNewPropertyOwnerInfo(PreviewNewPropertyOwnerInfoRequest request) {
        PreviewNewPropertyOwnerInfoVO newPropertyOwnerInfoVO = new PreviewNewPropertyOwnerInfoVO();
        String instanceId = request.getInstanceId();
        String formData = request.getFormData();
        // 主页原产权人信息
        BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
        Object o = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode());
        JSONObject jsonObject = JSONObject.from(o);
        HomePageFromDataDTO homePageFromDataDTO = JSON.parseObject(jsonObject.getString("formData"), HomePageFromDataDTO.class);
        // 继承人信息
        HeirInfoFillFormDataDTO heirInfoFillFormDataDTO = JSON.parseObject(formData, HeirInfoFillFormDataDTO.class);
        // 获取新产权人信息
        newPropertyOwnerInfoVO = getNewPropertyOwnerInfo(homePageFromDataDTO, heirInfoFillFormDataDTO);
        newPropertyOwnerInfoVO.setInstanceId(instanceId);
        return ResponseData.success(newPropertyOwnerInfoVO);
    }

    @Override
    public ResponseData<HeirInfoSupplementVO> getHeirInfoSupplementFrom(String instanceId) {
        HeirInfoSupplementVO heirInfoSupplementVO = new HeirInfoSupplementVO();
        BizInstanceInfo instanceInfo = bizInstanceInfoService.queryById(instanceId);
        // todo 如果有之前已经提交的数据，可能需要处理
//        HeirInfoSupplementVO infoSupplementOld = null;
//        if (bizInstanceInfo.extendJsonGet(BizInstanceExtendJsonEnum.HEIR_INFO_SUPPLEMENT.getCode()) != null) {
//            Object o = bizInstanceInfo.extendJsonGet(BizInstanceExtendJsonEnum.HEIR_INFO_SUPPLEMENT.getCode());
//            infoSupplementOld = JSON.parseObject(String.valueOf(o), HeirInfoSupplementVO.class);
//        }
        // 原产权人信息
        heirInfoSupplementVO.setOwnerInfoList(
                (List<OriginalPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo));
        // 签名文件预览
        heirInfoSupplementVO.setSignatureFilePreviewList(
                (List<SignatureFilePreviewVO>) BizInstanceExtendJsonEnum.SIGNATURE_FILE_PREVIEW_VO.convertObject(instanceInfo));
        // 新产权人信息
        List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList = (List<NewPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo);
        heirInfoSupplementVO.setNewPropertyOwnerInfoList(newPropertyOwnerInfoVOList);
        // 放弃登薄人信息
        List<AbandonDengBoInfoVO> abandonDengBoInfoVOList = (List<AbandonDengBoInfoVO>) BizInstanceExtendJsonEnum.ABANDON_DENG_BO_INFO_VO.convertObject(instanceInfo);
        heirInfoSupplementVO.setAbandonDengBoInfoList(abandonDengBoInfoVOList);

        // 设置是否签字和是否在线签字
        bizInstanceInheritSignatureService.setSignatureSituation(instanceId, newPropertyOwnerInfoVOList, abandonDengBoInfoVOList);

        heirInfoSupplementVO.setInstanceId(instanceId);
        return ResponseData.success(heirInfoSupplementVO);
    }

    @Override
    public ResponseData<Void> reSignature(String instanceId, String idNumber) throws Exception {
        bizInstanceInheritSignatureService.reSignature(instanceId, idNumber);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> signatureSubmit(SignatureSubmitRequest submitRequest) throws Exception {
        bizInstanceInheritSignatureService.signatureSubmit(submitRequest.getInstanceId(), submitRequest.getIdNumber()
                , submitRequest.getFile());
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<String> heirInfoSupplementFromSubmit(HeirInfoSupplementSubmitRequest supplementSubmitRequest) {
        String instanceId = supplementSubmitRequest.getInstanceId();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
//        HeirInfoSupplementVO heirInfoSupplementVO = getHeirInfoSupplementFrom(instanceId).getData();
        // todo 根据supplementSubmitRequest处理heirInfoSupplementVO
        // 保存
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put(BizInstanceExtendJsonEnum.HEIR_INFO_SUPPLEMENT.getCode(), heirInfoSupplementVO);
//        bizInstanceInfo.extendJsonAdd(jsonObject);

        // 设置申报暂存当前停留界面
        bizInstanceInheritService.setInheritanceStagePage(instanceId, InheritanceStagePageEnum.TAX_RETURN);
        return ResponseData.success().build();
    }


    @Override
    public ResponseData<Void> startProcessSubmit(StartProcessSubmitRequest startProcessSubmitRequest) {
        String instanceId = startProcessSubmitRequest.getInstanceId();
        BizInstanceInherit bizInstanceInherit = bizInstanceInheritService.getByInstanceId(instanceId);
        String currentNode = bizInstanceInherit.getCurrentNode();
        // 预受理提交变为税务审核
        if (InheritanceNodeStatusEnum.PRE_ACCEPTANCE.name().equals(currentNode)) {
            bizInstanceInheritService.setInheritanceCurrentNode(instanceId, InheritanceNodeStatusEnum.TAX_AUDIT
                    , "");
        } else if (InheritanceNodeStatusEnum.TAX_RETURN.name().equals(currentNode)) {
            // 税务退回重新提交变为税务审核
            bizInstanceInheritService.setInheritanceCurrentNode(instanceId, InheritanceNodeStatusEnum.TAX_AUDIT
                    , "");
        } else if (InheritanceNodeStatusEnum.REALESTATE_RETURN.name().equals(currentNode)) {
            // 不动产退回重新提交变为不动产受理
            bizInstanceInheritService.setInheritanceCurrentNode(instanceId, InheritanceNodeStatusEnum.WAIT_REALESTATE_ACCEPTANCE
                    , "");
        }
        // 设置申报暂存当前停留界面
        bizInstanceInheritService.setInheritanceStagePage(instanceId, InheritanceStagePageEnum.END);
        return ResponseData.success().build();
    }

    /**
     * 不动产查询
     *
     * @param query
     * @return
     */
    private RealEstateDTO realRealEstateQuery(RealEstateRequest query) {
        realEstateQueryUrl += "?propertyRightName=" + query.getPropertyRightName() + "&propertyRightCertificateNo=" + query.getPropertyRightCertificateNo();
        String accessTokenResult = restTemplate.getForObject(realEstateQueryUrl, String.class);
        RealEstateDTO realEstateDTO = JSON.parseObject(accessTokenResult, RealEstateDTO.class);
        if (!realEstateDTO.getSuccess()) {
            throw new BusinessException("不动产登记簿查询失败：" + realEstateDTO.getMessage());
        }
        if (realEstateDTO.getData() == null && CollUtil.isEmpty(realEstateDTO.getData().getQllist())) {
            throw new BusinessException("不动产登记簿查询失败");
        }
        return realEstateDTO;
    }

    /**
     * 转换对象
     *
     * @param realEstateDTO
     */
    private RealEstateVO convertingEntities(RealEstateDTO realEstateDTO) {
        RealEstateVO realEstateVO = new RealEstateVO();
        RealEstateDTO.Qllist qllist = realEstateDTO.getData().getQllist().get(0);
        realEstateVO.setBdcqzh(qllist.getBdcqzh());
        realEstateVO.setFwzl(StringUtils.isEmpty(qllist.getFwzl()) ?
                (CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getFwzl())
                :
                qllist.getFwzl());
        realEstateVO.setZcs(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getZcs());
        realEstateVO.setSzc(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getSzc());
        realEstateVO.setMj(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getMj());
        realEstateVO.setFwjg(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getFwjg());
        realEstateVO.setFwyt(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getFwyt());
        realEstateVO.setTdyt(CollUtil.isEmpty(qllist.getTdxxlist()) ? "" : qllist.getTdxxlist().get(0).getYt());
        realEstateVO.setTdsymj(CollUtil.isEmpty(qllist.getTdxxlist()) ? "" : qllist.getTdxxlist().get(0).getSyqmj());
        realEstateVO.setQllx(StringUtils.isEmpty(qllist.getQllx()) ? "" : qllist.getQllx());
        realEstateVO.setQlxz(StringUtils.isEmpty(qllist.getQlxz()) ? "" : qllist.getQlxz());
        realEstateVO.setTdsykssj(CollUtil.isEmpty(qllist.getTdxxlist()) ? "" : qllist.getTdxxlist().get(0).getTdsykssj());
        realEstateVO.setTdsyjssj(CollUtil.isEmpty(qllist.getTdxxlist()) ? "" : qllist.getTdxxlist().get(0).getTdsyjssj());
        realEstateVO.setFj(StringUtils.isEmpty(qllist.getFj()) ? "" : qllist.getFj());
        realEstateVO.setGyfs(StringUtils.isEmpty(qllist.getGyfs()) ? "" : qllist.getGyfs());
        if (StringUtils.isNotEmpty(qllist.getXzzt())) {
            if (qllist.getXzzt().contains("查封")) {
                realEstateVO.setFwcfqk(SeizureType.EXIST_SEIZURE.getValue());
                realEstateVO.setFwdyqk(MortgageType.NO_MORTGAGE.getValue());
            } else if (qllist.getXzzt().contains("抵押")) {
                realEstateVO.setFwdyqk(qllist.getDyaqxxlist().size() > 1 ? MortgageType.MORTGAGE_MULTI.getValue() : MortgageType.MORTGAGE_ONCE.getValue());
                realEstateVO.setFwcfqk(SeizureType.NO_EXIST_SEIZURE.getValue());
            } else {
                realEstateVO.setFwdyqk(qllist.getXzzt());
                realEstateVO.setFwcfqk(qllist.getXzzt());
            }
        } else {
            realEstateVO.setFwdyqk(MortgageType.NO_MORTGAGE.getValue());
            realEstateVO.setFwcfqk(SeizureType.NO_EXIST_SEIZURE.getValue());
        }
        // 扩展
        realEstateVO.setFwxz(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getFwxz());
        realEstateVO.setBdcdyh(CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getBdcdyh());
        return realEstateVO;
    }

    /**
     * 获取原权利人姓名
     *
     * @param realEstateDTO
     * @return
     */
    private String getYcqrxm(RealEstateDTO realEstateDTO) {
        RealEstateDTO.Qllist qllist = realEstateDTO.getData().getQllist().get(0);
        return StringUtils.isEmpty(qllist.getQlr()) ?
                (CollUtil.isEmpty(qllist.getQlrxxlist()) ? "" : qllist.getQlrxxlist().stream().map(v -> v.getQlr()).collect(Collectors.joining("/")))
                :
                qllist.getQlr();
    }

    /**
     * 生成不动产登记簿记录文件
     *
     * @param realEstateDTO
     * @return
     * @throws Exception
     */
    private String generateRealEstateRegisterFile(RealEstateDTO realEstateDTO) throws Exception {
        // 拿到材料模板
        ConfMaterials confMaterials = confMaterialsService.queryForSingle(
                ImmutableMap.of("=(materialCode)", BusinessConstants.REALESTATE_REGISTER_BOOK_MATERIALS_CODE));
        if (confMaterials == null || CollUtil.isEmpty(confMaterials.getBackTable())) {
            throw new BusinessException("不动产登记簿查询记录渲染模板不存在！");
        }
        ConfMaterialFile confMaterialFile = confMaterials.getBackTable().get(0);
        InputStream inputStream = sysFileEntityService.getInputStream(confMaterialFile.getFileId());

        // 生成渲染报表
        log.info("目前正在渲染不动产登记簿模板:");
        log.info("文件ID:" + confMaterialFile.getFileId());
        log.info("文件名称:" + confMaterialFile.getFileName());
        // 配置循环列表
        List<String> arrayKeyList = Arrays.asList("fwxxlist", "tdxxlist", "fsssxxlist", "qlrxxlist", "dyaqxxlist");
        Configure configure = WordRenderUtils.configureBuilderBindLoopRowTables(arrayKeyList).useSpringEL(false).build();
        // 替换后缀
        String fileName = WordRenderUtils.replaceFileExtension(confMaterialFile.getFileName(), "pdf");
        // 转换渲染对象
        JSONObject jsonObject = convertRenderedObjects(realEstateDTO);
        // 开始渲染
        SysFileUploadModel sysFileUploadModel = WordRenderUtils.renderAndReturnSysFileUploadModel(inputStream, fileName, jsonObject, configure);
        SysFileEntity sysFileEntity = sysFileEntityService.upload(sysFileUploadModel);
        return sysFileEntity.getId();
    }

    /**
     * 转换渲染对象
     *
     * @param realEstateDTO
     * @return
     */
    private JSONObject convertRenderedObjects(RealEstateDTO realEstateDTO) {
        RealEstateDTO.Qllist qllist = realEstateDTO.getData().getQllist().get(0);
        JSONObject jsonObject = new JSONObject();
        // 查询时间
        jsonObject.put("cxsj", DateTimeUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss"));
        // 查询编号
        jsonObject.put("cxbh", realEstateDTO.getData().getYwbh());

        //<editor-fold desc="权证号信息">
        // 产权证号
        jsonObject.put("bdcqzh", qllist.getBdcqzh());
        // 不动产单元号
        jsonObject.put("bdcdyh", StringUtils.isEmpty(qllist.getBdcdyh()) ?
                (CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getBdcdyh())
                :
                qllist.getBdcdyh());
        // 权利人
        jsonObject.put("qlr", StringUtils.isEmpty(qllist.getQlr()) ?
                (CollUtil.isEmpty(qllist.getQlrxxlist()) ? "" : qllist.getQlrxxlist().stream().map(v -> v.getQlr()).collect(Collectors.joining("/")))
                :
                qllist.getQlr());
        // 权利人证件号
        jsonObject.put("qlrzjh", StringUtils.isEmpty(qllist.getQlrzjh()) ?
                (CollUtil.isEmpty(qllist.getQlrxxlist()) ? "" : qllist.getQlrxxlist().stream().map(v -> v.getQlrzjh()).collect(Collectors.joining("/")))
                :
                qllist.getQlrzjh());
        // 登记日期
        jsonObject.put("djsj", qllist.getDjsj());
        // 查询信息用途
        jsonObject.put("cxyt", "");
        // 证书状态
        jsonObject.put("qszt", qllist.getQszt());
        // 附记
        jsonObject.put("fj", StringUtils.isEmpty(qllist.getFj()) ? "" : qllist.getFj());
        //</editor-fold>

        // 房屋信息
        qllist.getFwxxlist().forEach(v -> v.setFwhsxh(qllist.getXh().toString()));
        jsonObject.put("fwxxlist", qllist.getFwxxlist());
        // 土地信息
        String fwzl = StringUtils.isEmpty(qllist.getFwzl()) ?
                (CollUtil.isEmpty(qllist.getFwxxlist()) ? "" : qllist.getFwxxlist().get(0).getFwzl())
                :
                qllist.getFwzl();
        qllist.getTdxxlist().forEach(v -> v.setFwzl(fwzl));
        jsonObject.put("tdxxlist", qllist.getTdxxlist());
        // 附属设施信息
        qllist.getTdxxlist().forEach(v -> v.setFwzl(fwzl));
        jsonObject.put("fsssxxlist", qllist.getFsssxxlist());
        // 共有权人
        jsonObject.put("qlrxxlist", qllist.getQlrxxlist());
        // 他项权利信息
        jsonObject.put("dyaqxxlist", qllist.getDyaqxxlist());
        // 查封登记
        jsonObject.put("cfdj", CollUtil.isEmpty(qllist.getCfxxlist()) ? "无" : "有");
        // 锁定情况
        jsonObject.put("sdqk", "无");
        // 预告登记
        jsonObject.put("ygdj", CollUtil.isEmpty(qllist.getYgxxlist()) ? "无" : "有");
        // 异议登记
        jsonObject.put("yydj", CollUtil.isEmpty(qllist.getYyxxlist()) ? "无" : "有");
        // 地役权登记
        jsonObject.put("dyqdj", "无");
        // 居住权登记
        jsonObject.put("jzqdj", "无");
        // 时间
        jsonObject.put("date", DateTimeUtils.format(new Date(), "yyyy-MM-dd"));
        return jsonObject;
    }

    /**
     * 主页-原产权人表单数据填充
     *
     * @param estateDTO
     * @return
     */
    private HomePageFromDataDTO fillOriginalOwnerFormData(RealEstateDTO estateDTO) {
        RealEstateDTO.Qllist qllist = estateDTO.getData().getQllist().get(0);
        HomePageFromDataDTO homePageFromDataDTO = new HomePageFromDataDTO();
        List<HomePageFromDataDTO.Ycqrxx> ycqrxxTable = new ArrayList<>();
        if (CollUtil.isEmpty(qllist.getQlrxxlist())) {
            return homePageFromDataDTO;
        }
        HomePageFromDataDTO.Ycqrxx ycqrxx = null;
        for (RealEstateDTO.Qlrxxlist qlrxx : qllist.getQlrxxlist()) {
            ycqrxx = new HomePageFromDataDTO.Ycqrxx();
            ycqrxx.setYcqrxm(qlrxx.getQlr());
//            ycqrxx.setYcqrdqsmzt("在世");
            ycqrxx.setYcqrsfzhm(qlrxx.getQlrzjh());
//            ycqrxx.setYcqrsfjcszfe("不继承");
//            ycqrxx.setYcqrsfydlr("无");
            ycqrxx.setYcqrqmfs("现场签名");
//            ycqrxx.setSfyyxgyr("无");
            // 份额
            if (StringUtils.isNotEmpty(qlrxx.getGyfs())
                    && qlrxx.getGyfs().equals("共同共有")) {
                // 计算共同共有每个人比例
                String ycqrsyfe = "";
                int num = qllist.getQlrxxlist().size();
                ycqrsyfe = FractionOrPercentageUtil.calculateResult(num);
                ycqrxx.setYcqrsyfe(ycqrsyfe);
                homePageFromDataDTO.setFegs(FractionOrPercentageUtil.checkType(ycqrsyfe));
            } else {
                // 如果不是共同共有，直接从字段里取
                ycqrxx.setYcqrsyfe(qlrxx.getQlbl());
                // 判断字段类型是分数还是百分数
                homePageFromDataDTO.setFegs(FractionOrPercentageUtil.checkType(qlrxx.getQlbl()));
            }

            ycqrxxTable.add(ycqrxx);
        }
        List<HomePageFromDataDTO.Dlrxx> dlrxxTable = new ArrayList<>();
        HomePageFromDataDTO.Dlrxx dlrxx = new HomePageFromDataDTO.Dlrxx();
        dlrxxTable.add(dlrxx);
        homePageFromDataDTO.setDlrxxTable(dlrxxTable);
        homePageFromDataDTO.setYcqrxxTable(ycqrxxTable);
        return homePageFromDataDTO;
    }

    /**
     * 通过原产权人信息获取被继承人信息
     *
     * @param prePageFormData 主页表单数据
     * @param formData        老的被继承人表单数据
     * @return
     */
    private HeirInfoFillFormDataDTO beHeirInfoAnalysis(String prePageFormData, String formData) {
        // 是否有老的继承人信息formData
        boolean hasOldFormData = StringUtils.isNotEmpty(formData);

        // 主页表单FormData对象
        HomePageFromDataDTO homePageFromDataDTO = JSON.parseObject(prePageFormData, HomePageFromDataDTO.class);
        // 被继承人信息列表
        HeirInfoFillFormDataDTO heirInfoFillFormData = new HeirInfoFillFormDataDTO();
        List<HeirInfoFillFormDataDTO.Bjcrxx> bjcrxxList = new ArrayList<>();
        if (!hasOldFormData) {
            // 获得被继承人信息列表
            bjcrxxList = beHeirInfoListFill(homePageFromDataDTO);
        } else {
            // 获得被继承人信息列表，并且处理老的继承人信息
            HeirInfoFillFormDataDTO heirInfoFillFormDataOld = JSON.parseObject(formData, HeirInfoFillFormDataDTO.class);
            heirInfoFillFormData.setFegs(heirInfoFillFormDataOld.getFegs());
            heirInfoFillFormData.setXbdcqzgyfs(heirInfoFillFormDataOld.getXbdcqzgyfs());
            bjcrxxList = beHeirInfoListFill(homePageFromDataDTO, heirInfoFillFormDataOld);
        }

        // 填充一下空的列表数据
        for (HeirInfoFillFormDataDTO.Bjcrxx bjcrxx : bjcrxxList) {
            if (CollUtil.isNotEmpty(bjcrxx.getJcrxxTable())) {
                continue;
            }
            List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = new ArrayList<>();
            HeirInfoFillFormDataDTO.Jcrxx jcrxx = new HeirInfoFillFormDataDTO.Jcrxx();
            jcrxxTable.add(jcrxx);
            bjcrxx.setJcrxxTable(jcrxxTable);
        }

        heirInfoFillFormData.setBjcrxxTable(bjcrxxList);
        return heirInfoFillFormData;
    }

    /**
     * 获得被继承人信息列表
     *
     * @param homePageFromDataDTO
     * @return
     */
    private List<HeirInfoFillFormDataDTO.Bjcrxx> beHeirInfoListFill(HomePageFromDataDTO homePageFromDataDTO) {
        // 被继承人列表
        List<HeirInfoFillFormDataDTO.Bjcrxx> bjcrxxList = new ArrayList<>();
        HeirInfoFillFormDataDTO.Bjcrxx bjcrxx = null;
        // 原产权人列表
        List<HomePageFromDataDTO.Ycqrxx> ycqrxxTable = homePageFromDataDTO.getYcqrxxTable();
        // 拿到原产权人死亡列表（被继承人列表）
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxTable) {
            // 原产权人是否已经死亡，如果死亡就是被继承人
            boolean death = ycqrxx.getYcqrdqsmzt().equals("死亡");
            if (death) {
                bjcrxx = new HeirInfoFillFormDataDTO.Bjcrxx();
                bjcrxx.setBjcrxm(ycqrxx.getYcqrxm());
                bjcrxx.setBjcrsfzhm(ycqrxx.getYcqrsfzhm());
                bjcrxx.setBjcrdqsmzt(ycqrxx.getYcqrdqsmzt());
                bjcrxx.setBjcrsyfe(ycqrxx.getYcqrsyfe());
                bjcrxxList.add(bjcrxx);
                if (ycqrxx.getSfyyxgyr().equals("有")) {
                    bjcrxx.setYyxgyrsfzhm(ycqrxx.getYyxgyrsfzhm());
                }
            }
            // 原隐形共有人是否已经死亡，如果死亡就是被继承人
            boolean hasYyxgyr = ycqrxx.getSfyyxgyr().equals("有");
            if (!hasYyxgyr) {
                continue;
            }
            boolean deathYxgyr = ycqrxx.getYyxgyrdqsmzt().equals("死亡");
            if (deathYxgyr) {
                bjcrxx = new HeirInfoFillFormDataDTO.Bjcrxx();
                bjcrxx.setBjcrxm(ycqrxx.getYyxgyrxm());
                bjcrxx.setBjcrsfzhm(ycqrxx.getYyxgyrsfzhm());
                bjcrxx.setBjcrdqsmzt(ycqrxx.getYyxgyrdqsmzt());
                bjcrxx.setBjcrsyfe(ycqrxx.getYyxgyrsyfe());
                bjcrxxList.add(bjcrxx);
            }
        }
        // 被继承人列表-填充继承人信息列表
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxTable) {
            // 原产权人是否在世，如果在世并且继承逝者份额就是继承人
            boolean inherit = ycqrxx.getYcqrdqsmzt().equals("在世") && ycqrxx.getYcqrsfjcszfe().equals("继承");
            if (inherit) {
                for (HeirInfoFillFormDataDTO.Bjcrxx bjcrxxSub : bjcrxxList) {
                    if (bjcrxxSub.getJcrxxTable() == null) {
                        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = new ArrayList<>();
                        bjcrxxSub.setJcrxxTable(jcrxxTable);
                    }
                    List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = bjcrxxSub.getJcrxxTable();
                    HeirInfoFillFormDataDTO.Jcrxx jcrxx = new HeirInfoFillFormDataDTO.Jcrxx();
                    jcrxx.setJcrxm(ycqrxx.getYcqrxm());
                    jcrxx.setJcrsfzhm(ycqrxx.getYcqrsfzhm());
                    jcrxx.setJcrsjhm(ycqrxx.getYcqrsjhm());
                    jcrxx.setJcrsfydlr(ycqrxx.getYcqrsfydlr());
                    jcrxx.setJcrdlrxx(ycqrxx.getYcqrdlrxx());
                    jcrxx.setJcrqmfs(ycqrxx.getYcqrqmfs());
                    jcrxx.setJcrsfsyyxgyr("否");
                    jcrxx.setJcrsfsdxz("否");
                    jcrxxTable.add(jcrxx);
                }
            }
            // 原隐形共有人是否在世，如果在世并且继承逝者份额就是继承人
            boolean hasYxgyr = ycqrxx.getSfyyxgyr().equals("有");
            if (!hasYxgyr) {
                continue;
            }
            boolean inheritYyxgyr = ycqrxx.getYyxgyrdqsmzt().equals("在世") && ycqrxx.getYyxgyrsfjcszfe().equals("继承");
            if (inheritYyxgyr) {
                for (HeirInfoFillFormDataDTO.Bjcrxx bjcrxxSub : bjcrxxList) {
                    // 继承人是原隐形共有人默认只能继承对应的原产权人
                    boolean isYcxgr = ycqrxx.getYyxgyrsfzhm().equals(bjcrxxSub.getYyxgyrsfzhm());
                    if (!isYcxgr) {
                        continue;
                    }
                    if (bjcrxxSub.getJcrxxTable() == null) {
                        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = new ArrayList<>();
                        bjcrxxSub.setJcrxxTable(jcrxxTable);
                    }
                    List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = bjcrxxSub.getJcrxxTable();
                    HeirInfoFillFormDataDTO.Jcrxx jcrxx = new HeirInfoFillFormDataDTO.Jcrxx();
                    jcrxx.setJcrxm(ycqrxx.getYyxgyrxm());
                    jcrxx.setJcrsfzhm(ycqrxx.getYyxgyrsfzhm());
                    jcrxx.setJcrsjhm(ycqrxx.getYyxgyrsjhm());
                    jcrxx.setJcrsfydlr(ycqrxx.getYyxgyrsfydlr());
                    jcrxx.setJcrdlrxx(ycqrxx.getYyxgyrdlrxx());
                    jcrxx.setJcrqmfs(ycqrxx.getYyxgyrqmfs());
                    jcrxx.setJcrsfsyyxgyr("是");
                    jcrxx.setJcrsfsdxz("否");
                    jcrxxTable.add(jcrxx);
                }
            }
        }
        return bjcrxxList;
    }

    /**
     * 获得被继承人信息列表，并且处理老的继承人信息
     *
     * @param homePageFromDataDTO     原产权人信息
     * @param heirInfoFillFormDataOld 老的被继承人信息
     * @return
     */
    private List<HeirInfoFillFormDataDTO.Bjcrxx> beHeirInfoListFill(HomePageFromDataDTO homePageFromDataDTO, HeirInfoFillFormDataDTO heirInfoFillFormDataOld) {
        // 获得被继承人信息列表
        List<HeirInfoFillFormDataDTO.Bjcrxx> bjcrxxList = beHeirInfoListFill(homePageFromDataDTO);
        // 获得老的被继承人信息列表
        List<HeirInfoFillFormDataDTO.Bjcrxx> bjcrxxListOld = heirInfoFillFormDataOld.getBjcrxxTable();
        Map<String, HeirInfoFillFormDataDTO.Bjcrxx> bjcrxxListOldMap = bjcrxxListOld.stream().collect(Collectors.toMap(k -> k.getBjcrsfzhm(), v -> v));

        // 处理被继承人信息
        for (HeirInfoFillFormDataDTO.Bjcrxx bjcrxx : bjcrxxList) {
            String bjcrsfzhm = bjcrxx.getBjcrsfzhm();
            if (!bjcrxxListOldMap.containsKey(bjcrsfzhm)) {
                continue;
            }
            // 新的继承人信息列表
            if (bjcrxx.getJcrxxTable() == null) {
                bjcrxx.setJcrxxTable(new ArrayList<>());
            }
            List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTable = bjcrxx.getJcrxxTable();
            // 老的继承人信息列表
            HeirInfoFillFormDataDTO.Bjcrxx bjcrxxOld = bjcrxxListOldMap.get(bjcrsfzhm);
            List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxTableOld = bjcrxxOld.getJcrxxTable();
            Map<String, HeirInfoFillFormDataDTO.Jcrxx> jcrxxTableOldMap = jcrxxTableOld.stream().collect(Collectors.toMap(k -> k.getJcrsfzhm(), v -> v, (v1, v2) -> v1));

            /*
             * 新的继承人信息列表和老的继承人信息列表对比差异
             * 1.新的继承人信息列表中如果有继承人（继承人是否手动新增）肯定为否
             * 2.合并老的继承人信息列表中配偶信息
             * 3.合并老的继承人信息列表中（继承人是否手动新增）为是的继承人
             */
            // 合并老的继承人信息列表中配偶信息
            for (HeirInfoFillFormDataDTO.Jcrxx jcrxx : jcrxxTable) {
                String jcrsfzhm = jcrxx.getJcrsfzhm();
                if (!jcrxxTableOldMap.containsKey(jcrsfzhm)) {
                    continue;
                }
                HeirInfoFillFormDataDTO.Jcrxx jcrxxOld = jcrxxTableOldMap.get(jcrsfzhm);
                jcrxx.setJcrjcfe(jcrxxOld.getJcrjcfe());
                jcrxx.setJcrhyzk(jcrxxOld.getJcrhyzk());
                if (!jcrxxOld.getJcrhyzk().equals("已婚")) {
                    continue;
                }
                // 设置配偶信息
                jcrxx.setJcrpoxm(jcrxxOld.getJcrpoxm());
                jcrxx.setJcrposfzhm(jcrxxOld.getJcrposfzhm());
                jcrxx.setJcrposjh(jcrxxOld.getJcrposjh());
                jcrxx.setJcrposfdb(jcrxxOld.getJcrposfdb());
                jcrxx.setJcrpodbfe(jcrxxOld.getJcrpodbfe());
                jcrxx.setJcrpoqmfs(jcrxxOld.getJcrpoqmfs());
                jcrxx.setJcrposfydlr(jcrxxOld.getJcrposfydlr());
                jcrxx.setJcrpodlrxx(jcrxxOld.getJcrpodlrxx());
            }
            // 合并老的继承人信息列表中（继承人是否手动新增）保存是的继承人
            for (HeirInfoFillFormDataDTO.Jcrxx jcrxxOld : jcrxxTableOld) {
                // 是否手动新增
                boolean sfsdxz = ((jcrxxOld.getJcrsfsdxz().equals("是")) || (StringUtils.isEmpty(jcrxxOld.getJcrsfsdxz())));
                if (!sfsdxz) {
                    continue;
                }
                jcrxxTable.add(jcrxxOld);
            }
        }

        return bjcrxxList;
    }

    /**
     * 保存原产权人信息,签名文件预览,新产权人信息,放弃登薄人信息到instanceExtendJson
     *
     * @param bizInstanceInfo
     * @param instanceFields
     * @return
     */
    private JSONObject saveInstanceExtendJson(BizInstanceInfo bizInstanceInfo, BizInstanceFields instanceFields) throws Exception {
        JSONObject jsonObject = new JSONObject();
        String instanceId = instanceFields.getInstance().getId();

        // 原产权人信息
        List<OriginalPropertyOwnerInfoVO> ownerInfoList = new ArrayList<>();
        OriginalPropertyOwnerInfoVO originalPropertyOwnerInfo = null;
        Object object = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HOME_PAGE_FROM_JSON.getCode());
        HomePageFromDataDTO homePageFromDataDTO = JSON.parseObject(JSONObject.from(object).getString("formData"), HomePageFromDataDTO.class);
        List<HomePageFromDataDTO.Ycqrxx> ycqrxxTable = homePageFromDataDTO.getYcqrxxTable();
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxTable) {
            originalPropertyOwnerInfo = new OriginalPropertyOwnerInfoVO(
                    false, ycqrxx.getYcqrxm(), ycqrxx.getYcqrsfzhm(), ycqrxx.getYcqrdqsmzt(), ycqrxx.getYcqrsyfe(), ycqrxx
            );
            ownerInfoList.add(originalPropertyOwnerInfo);
            if ("无".equals(ycqrxx.getSfyyxgyr())) {
                continue;
            }
            originalPropertyOwnerInfo = new OriginalPropertyOwnerInfoVO(
                    true, ycqrxx.getYyxgyrxm(), ycqrxx.getYyxgyrsfzhm(), ycqrxx.getYyxgyrdqsmzt(), ycqrxx.getYyxgyrsyfe(), ycqrxx
            );
            ownerInfoList.add(originalPropertyOwnerInfo);
        }
        jsonObject.put(BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.getCode(), ownerInfoList);

        // 新产权人信息
        Object object0 = instanceFields.getFormJsonMap().get(BizInstanceFieldsFormJsonMapEnum.HEIR_INFO_FILL_FROM_JSON.getCode());
        HeirInfoFillFormDataDTO heirInfoFillFormDataDTO = JSON.parseObject(JSONObject.from(object0).getString("formData"), HeirInfoFillFormDataDTO.class);
        PreviewNewPropertyOwnerInfoVO newPropertyOwnerInfoVO = getNewPropertyOwnerInfo(homePageFromDataDTO, heirInfoFillFormDataDTO);
        jsonObject.put(BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.getCode(), newPropertyOwnerInfoVO.getPropertyOwnerInfoList());

        // 放弃登薄人信息
        jsonObject.put(BizInstanceExtendJsonEnum.ABANDON_DENG_BO_INFO_VO.getCode(), newPropertyOwnerInfoVO.getAbandonDengBoInfoList());

        // 签名文件预览
        List<SignatureFilePreviewVO> signatureFilePreviewList = generateBackFillFile(bizInstanceInfo, jsonObject, heirInfoFillFormDataDTO);
        jsonObject.put(BizInstanceExtendJsonEnum.SIGNATURE_FILE_PREVIEW_VO.getCode(), signatureFilePreviewList);
        return jsonObject;
    }

    /**
     * 生成签名文件预览
     * 目前只回填渲染不动产登记申请书
     *
     * @param bizInstanceInfo
     * @param jsonObject              原产权人新产权人信息
     * @param heirInfoFillFormDataDTO 继承人信息
     * @return
     */
    private List<SignatureFilePreviewVO> generateBackFillFile(BizInstanceInfo bizInstanceInfo, JSONObject jsonObject
            , HeirInfoFillFormDataDTO heirInfoFillFormDataDTO) throws Exception {
        List<SignatureFilePreviewVO> signatureFilePreviewVOList = new ArrayList<>();

        // 申报材料
        List<BizInstanceMaterialVO> materialVOList = bizInstanceMaterialService.getInstanceMaterialNonInit(bizInstanceInfo.getId());
        // 筛选出需要回填的材料 - 签章或者回填满足其一
        List<BizInstanceMaterialVO> backTableList = materialVOList
                .stream()
                .filter(v -> (v.isSign() || v.isBackFill()) && CollUtil.isNotEmpty(v.getBackTable()))
                .toList();
        if (CollUtil.isEmpty(backTableList)) {
            log.info("没有找到需要回填的表");
            return signatureFilePreviewVOList;
        }
        // 筛选出不动产登记申请书
        List<BizInstanceMaterialVO> bizInstanceMaterialVOList = backTableList.stream().filter(v -> BusinessConstants.REAL_ESTATE_REGISTRATION_APPLICATION.equals(v.getMaterialCode())).toList();
        if (CollUtil.isEmpty(bizInstanceMaterialVOList)) {
            log.info("没有找到不动产登记申请书");
            return signatureFilePreviewVOList;
        }
        // 开始循环渲染，其实只会循环一次
        for (BizInstanceMaterialVO bizInstanceMaterialVO : backTableList) {
            if (CollUtil.isEmpty(bizInstanceMaterialVO.getBackTable())) {
                continue;
            }
            // 删除老的回填表
            bizInstanceMaterialFileService.deleteByMaterialId(bizInstanceMaterialVO.getId());

            // 遍历所有回填模板，其实只会循环一次
            for (BizInstanceMaterial.MaterialFile materialFile : bizInstanceMaterialVO.getBackTable()) {
                log.info("目前正在渲染模板:");
                log.info("文件ID:" + materialFile.getFileId());
                log.info("文件名称:" + materialFile.getFileName());
                String fileName = null;
                SysFileEntity sysFileEntity = null;
                InputStream inputStream = sysFileEntityService.getInputStream(materialFile.getFileId());
                // 配置循环列表
                List<String> arrayKeyList = Arrays.asList("");
                Configure configure = WordRenderUtils.configureBuilderBindLoopRowTables(arrayKeyList).useSpringEL(false).build();
                // 替换后缀
                fileName = WordRenderUtils.replaceFileExtension(materialFile.getFileName(), "pdf");

                // 转换渲染对象
                List<OriginalPropertyOwnerInfoVO> propertyOwnerInfoVOList =
                        (List<OriginalPropertyOwnerInfoVO>) jsonObject.get(BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.getCode());
                List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList =
                        (List<NewPropertyOwnerInfoVO>) jsonObject.get(BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.getCode());
                RealEstateVO realEstateVO = (RealEstateVO) BizInstanceExtendJsonEnum.REAL_ESTATE_VO.convertObject(bizInstanceInfo);
                // 渲染对象
                JSONObject renderData = new JSONObject();
                // 转让方（名称）
                renderData.put("ycqrxm"
                        , propertyOwnerInfoVOList.stream().map(v -> v.getXm()).collect(Collectors.joining(",")));
                // 转让方证件号
                renderData.put("ycqrsfzhm"
                        , propertyOwnerInfoVOList.stream().map(v -> v.getSfzhm()).collect(Collectors.joining(",")));
                // 受让方共有情况，1共同共有|2按份共有|3单独所有
                renderData.put("xbdcqzgyfs", heirInfoFillFormDataDTO.getXbdcqzgyfs().equals("共同共有") ? "1" :
                        heirInfoFillFormDataDTO.getXbdcqzgyfs().equals("按份共有") ? "2" : "3");
                // 受让方名称份额
                StringBuilder builder = new StringBuilder();
                newPropertyOwnerInfoVOList.stream().forEach(v -> builder.append(v.getXm()).append("(").append(v.getZfe()).append(") "));
                renderData.put("srfmcfe", builder.toString());
                // 受让方身份证号
                renderData.put("srfsfzhm"
                        , newPropertyOwnerInfoVOList.stream().map(v -> v.getSfzhm()).collect(Collectors.joining(",")));
                // 受让方联系电话
                renderData.put("srflxdh"
                        , newPropertyOwnerInfoVOList.stream().map(v -> v.getSjhm()).collect(Collectors.joining(",")));
                // 受让方代理人姓名证件号
                StringBuilder builder0 = new StringBuilder();
                newPropertyOwnerInfoVOList.stream().forEach(v -> {
                    if (v.getSfydlr().equals("有")) {
                        builder0.append(v.getDlrxm()).append("(").append(v.getDlrsfzh()).append(")");
                    }
                });
                renderData.put("srfdlrxmzjh", builder0.toString());
                // 时间
                renderData.put("date", DateTimeUtils.format(new Date(), "yyyy/MM/dd"));
                // 不动产信息
                renderData.putAll(JSONObject.from(realEstateVO));

                // 开始渲染
                SysFileUploadModel sysFileUploadModel = WordRenderUtils.renderAndReturnSysFileUploadModel(inputStream, fileName, renderData, configure);
                sysFileEntity = sysFileEntityService.upload(sysFileUploadModel);

                SignatureFilePreviewVO signatureFilePreviewVO = new SignatureFilePreviewVO();
                signatureFilePreviewVO.setSn("1");
                signatureFilePreviewVO.setName(fileName);
                signatureFilePreviewVO.setFileId(sysFileEntity.getId());
                signatureFilePreviewVO.setFileSize(sysFileEntity.getFileSize());
                signatureFilePreviewVOList.add(signatureFilePreviewVO);

                // 如果材料回填,则回填的文件直接放入上传列表
                if (bizInstanceMaterialVO.isBackFill()) {
                    BizInstanceMaterialFile instanceMaterialFile = new BizInstanceMaterialFile();
                    instanceMaterialFile.setFileId(sysFileEntity.getId());
                    instanceMaterialFile.setFileName(sysFileEntity.getFileName());
                    instanceMaterialFile.setFileSize(sysFileEntity.getFileSize());
                    instanceMaterialFile.setFileType(sysFileEntity.getFileType());
                    BizInstanceMaterial instanceMaterial = bizInstanceMaterialService.queryById(bizInstanceMaterialVO.getId());
                    instanceMaterialFile.setInstanceMaterial(instanceMaterial);
                    bizInstanceMaterialFileService.save(instanceMaterialFile);
                }
            }
        }
        return signatureFilePreviewVOList;
    }

    /**
     * 获取新产权人信息
     * 1、原产权人
     * 2、原产权人，继承人
     * 3、原产权人，继承人配偶 （一般不是直系亲属，不可继承，但是分到夫妻继承的份额）
     * 4、继承人
     * 5、继承人配偶
     * 注：原产权人如果不是直系亲属不可继承
     *
     * @param homePageFromDataDTO
     * @param heirInfoFillFormDataDTO
     * @return
     */
    private PreviewNewPropertyOwnerInfoVO getNewPropertyOwnerInfo(HomePageFromDataDTO homePageFromDataDTO, HeirInfoFillFormDataDTO heirInfoFillFormDataDTO) {
        PreviewNewPropertyOwnerInfoVO newPropertyOwnerInfoVO = new PreviewNewPropertyOwnerInfoVO();
        // 新产权人信息列表
        List<NewPropertyOwnerInfoVO> propertyOwnerInfoList = new ArrayList<>();

        // 代理人列表
        List<HomePageFromDataDTO.Dlrxx> dlrxxList = homePageFromDataDTO.getDlrxxTable();
        Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap = dlrxxList.stream().collect(Collectors.toMap(k -> k.getDlrsfzhm(), v -> v));
        // 原产权人列表
        List<HomePageFromDataDTO.Ycqrxx> ycqrxxList = homePageFromDataDTO.getYcqrxxTable();
        // 继承人信息列表
        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList = heirInfoFillFormDataDTO.getBjcrxxTable().stream().flatMap(v -> v.getJcrxxTable().stream()).collect(Collectors.toList());

        // 1、原产权人
        originalOwner(ycqrxxList, jcrxxList, propertyOwnerInfoList, dlrxxMap);

        // 1、原产权人，继承人  2、原产权人，继承人配偶 3、继承人配偶
        originalInheritanceOwner(ycqrxxList, jcrxxList, propertyOwnerInfoList, dlrxxMap);

        // 1、继承人  2、原产权人，继承人配偶 3、继承人配偶
        inheritanceOwner(ycqrxxList, jcrxxList, propertyOwnerInfoList, dlrxxMap);

        // 生成放弃登薄人信息列表
        List<AbandonDengBoInfoVO> abandonDengBoInfoList = generateAbandonDengBoInfoList(jcrxxList, dlrxxMap);

        newPropertyOwnerInfoVO.setPropertyOwnerInfoList(propertyOwnerInfoList);
        newPropertyOwnerInfoVO.setAbandonDengBoInfoList(abandonDengBoInfoList);
        return newPropertyOwnerInfoVO;

    }

    /**
     * 原产权人
     *
     * @param ycqrxxList
     * @param propertyOwnerInfoList
     * @param dlrxxMap
     */
    private void originalOwner(List<HomePageFromDataDTO.Ycqrxx> ycqrxxList, List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList
            , List<NewPropertyOwnerInfoVO> propertyOwnerInfoList
            , Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap) {
        // 原产权人
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxList) {
            if (ycqrxx.getYcqrdqsmzt().equals("死亡") || ycqrxx.getYcqrsfjcszfe().equals("继承")) {
                continue;
            }
            // 原产权人-是否是继承人配偶,如果是就是 2、原产权人，继承人配偶，不应该在这里处理
            List<HeirInfoFillFormDataDTO.Jcrxx> ycqrJcrxxList = jcrxxList.stream()
                    .filter(v -> ycqrxx.getYcqrsfzhm().equals((v.getJcrposfzhm())) && "已婚".equals(v.getJcrhyzk())).toList();
            if (ycqrJcrxxList.size() > 0) {
                continue;
            }
            String ycqrdlrxx = ycqrxx.getYcqrdlrxx();
            String ycqrdlrsfzh = dlrxxMap.get(ycqrdlrxx) == null ? "" : dlrxxMap.get(ycqrdlrxx).getDlrsfzhm();
            String ycqrdlrsjh = dlrxxMap.get(ycqrdlrxx) == null ? "" : dlrxxMap.get(ycqrdlrxx).getDlrsjh();
            propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(NewPropertyOwnerTypeEnum.ORIGINAL.getValue()
                    , ycqrxx.getYcqrxm(), ycqrxx.getYcqrsfzhm(), ycqrxx.getYcqrsjhm(), ycqrxx.getYcqrsyfe(), "0%", ycqrxx.getYcqrsyfe()
                    , ycqrxx.getYcqrsfydlr(), ycqrdlrxx, ycqrdlrsfzh, ycqrdlrsjh, ycqrxx.getYcqrqmfs()));
        }
        // 原隐形共有人
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxList) {
            if (ycqrxx.getSfyyxgyr().equals("无")) {
                continue;
            }
            if (ycqrxx.getYyxgyrdqsmzt().equals("死亡") || ycqrxx.getYyxgyrsfjcszfe().equals("继承")) {
                continue;
            }
            // 原隐形共有人-是否是继承人配偶,如果是就是 2、原产权人，继承人配偶，不应该在这里处理
            List<HeirInfoFillFormDataDTO.Jcrxx> yyxgyrcrxxList = jcrxxList.stream()
                    .filter(v -> ycqrxx.getYyxgyrsfzhm().equals((v.getJcrposfzhm())) && "已婚".equals(v.getJcrhyzk())).toList();
            if (yyxgyrcrxxList.size() > 0) {
                continue;
            }
            String yyxgyrdlrxx = ycqrxx.getYyxgyrdlrxx();
            String yyxgyrdlrsfzh = dlrxxMap.get(yyxgyrdlrxx) == null ? "" : dlrxxMap.get(yyxgyrdlrxx).getDlrsfzhm();
            String yyxgyrdlrsjh = dlrxxMap.get(yyxgyrdlrxx) == null ? "" : dlrxxMap.get(yyxgyrdlrxx).getDlrsjh();
            String yyxgyrdlrxm = dlrxxMap.get(yyxgyrdlrxx) == null ? "" : dlrxxMap.get(yyxgyrdlrxx).getDlrxm();
            propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(NewPropertyOwnerTypeEnum.ORIGINAL.getValue()
                    , ycqrxx.getYyxgyrxm(), ycqrxx.getYyxgyrsfzhm(), ycqrxx.getYyxgyrsjhm(), ycqrxx.getYyxgyrsyfe(), "0%", ycqrxx.getYyxgyrsyfe()
                    , ycqrxx.getYyxgyrsfydlr(), yyxgyrdlrxm, yyxgyrdlrsfzh, yyxgyrdlrsjh, ycqrxx.getYyxgyrqmfs()));

        }
    }

    /**
     * 1、原产权人，继承人  2、原产权人，继承人配偶 3、继承人配偶
     *
     * @param ycqrxxList
     * @param jcrxxList
     * @param propertyOwnerInfoList
     * @param dlrxxMap
     */
    private static void originalInheritanceOwner(List<HomePageFromDataDTO.Ycqrxx> ycqrxxList
            , List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList
            , List<NewPropertyOwnerInfoVO> propertyOwnerInfoList
            , Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap
    ) {
        // 存放处理过的继承人配偶集合
//        Set<String> jcrpoSet = new HashSet<>();
        // 1、原产权人，继承人
        // 原产权人
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxList) {
            if (ycqrxx.getYcqrdqsmzt().equals("死亡") || ycqrxx.getYcqrsfjcszfe().equals("不继承")) {
                continue;
            }
            List<HeirInfoFillFormDataDTO.Jcrxx> ycqrJcrxxList = jcrxxList.stream().filter(v -> ycqrxx.getYcqrsfzhm().equals((v.getJcrsfzhm()))).toList();
            originalInheritanceOwnerSub(ycqrxxList, ycqrJcrxxList, propertyOwnerInfoList, dlrxxMap, ycqrxx, false);
        }
        // 原隐形共有人
        for (HomePageFromDataDTO.Ycqrxx ycqrxx : ycqrxxList) {
            if (ycqrxx.getSfyyxgyr().equals("无")) {
                continue;
            }
            if (ycqrxx.getYyxgyrdqsmzt().equals("死亡") || ycqrxx.getYyxgyrsfjcszfe().equals("不继承")) {
                continue;
            }
            // 如果有说明是继承人配偶,已经处理过了，则跳过
//            if (jcrpoSet.contains(ycqrxx.getYyxgyrsfzhm())) {
//                continue;
//            }
            // 拿到原产权人继承列表
            List<HeirInfoFillFormDataDTO.Jcrxx> yyxgyrJcrxxList = jcrxxList.stream().filter(v -> ycqrxx.getYyxgyrsfzhm().equals((v.getJcrsfzhm()))).toList();
            originalInheritanceOwnerSub(ycqrxxList, yyxgyrJcrxxList, propertyOwnerInfoList, dlrxxMap, ycqrxx, true);
        }
    }

    /**
     * 1、继承人  2、原产权人，继承人配偶 3、继承人配偶
     *
     * @param ycqrxxList
     * @param jcrxxList
     * @param propertyOwnerInfoList
     * @param dlrxxMap
     */
    private static void inheritanceOwner(List<HomePageFromDataDTO.Ycqrxx> ycqrxxList
            , List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList
            , List<NewPropertyOwnerInfoVO> propertyOwnerInfoList
            , Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap
    ) {
        // 原产权人信息列表,原隐形共有人信息列表
        Map<String, HomePageFromDataDTO.Ycqrxx> ycqrxxMap = ycqrxxList.stream().collect(Collectors.toMap(k -> k.getYcqrsfzhm(), v -> v));
        Map<String, HomePageFromDataDTO.Ycqrxx> yyxgyrxxMap = ycqrxxList.stream().collect(Collectors.toMap(k -> k.getYyxgyrsfzhm(), v -> v, (v1, v2) -> v1));
        // 继承人分组，继承人可能继承多个人，所以继承人列表会重复，所以需要分组
        Map<String, List<HeirInfoFillFormDataDTO.Jcrxx>> jcrxxMap = jcrxxList.stream().collect(Collectors.groupingBy(HeirInfoFillFormDataDTO.Jcrxx::getJcrsfzhm));
        for (Map.Entry<String, List<HeirInfoFillFormDataDTO.Jcrxx>> map : jcrxxMap.entrySet()) {
            String jcrsfzhm = map.getKey();
            jcrxxList = map.getValue();
            // 过滤继承人是否是原产权人或者原隐形共有人，如果是说明之前已经处理过了，不重复处理
            if (ycqrxxMap.containsKey(jcrsfzhm) || yyxgyrxxMap.containsKey(jcrsfzhm)) {
                continue;
            }
            // 1、继承人
            List<String> JcrjcfeList = jcrxxList.stream().map(v -> v.getJcrjcfe()).toList();
            String JcrjcfeTotal = FractionOrPercentageUtil.calculateSum(JcrjcfeList.toArray(new String[0])); // 继承份额
            String total = JcrjcfeTotal; // 总份额
            // 总份额减去继承人配偶登薄份额
            List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxpoList = jcrxxList.stream().filter(v -> "已婚".equals(v.getJcrhyzk())).toList();
            List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxpodbList = jcrxxpoList.stream().filter(v -> "登簿".equals(v.getJcrposfdb())).toList();
            String JcrpodbfeTotal = "0%";
            if (jcrxxpodbList.size() > 0) {
                List<String> JcrpodbfeList = jcrxxpodbList.stream().map(v -> v.getJcrpodbfe()).toList();
                JcrpodbfeTotal = FractionOrPercentageUtil.calculateSum(JcrpodbfeList.toArray(new String[0])); // 继承人配偶登薄份额
                total = FractionOrPercentageUtil.calculateSubtract(total, JcrpodbfeTotal); // 总份额
            }
            // 保存继承人
            HeirInfoFillFormDataDTO.Jcrxx jcrxx = jcrxxList.get(0);
            String dlrxx = jcrxx.getJcrdlrxx();
            String dlrsfzh = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrsfzhm();
            String dlrsjh = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrsjh();
            String dlrxm = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrxm();
            propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(
                    NewPropertyOwnerTypeEnum.INHERITANCE.getValue()
                    , jcrxx.getJcrxm(), jcrxx.getJcrsfzhm(), jcrxx.getJcrsjhm(), "0%", JcrjcfeTotal, total
                    , jcrxx.getJcrsfydlr(), dlrxm, dlrsfzh, dlrsjh, jcrxx.getJcrqmfs()));

            // 继承人配偶
            if (jcrxxpoList.size() == 0) {
                continue;
            }
            String jcrposfzhm = jcrxx.getJcrposfzhm();
            // 判断继承人配偶是不是原产权人或者原隐形共有人
            String type = "";
            String ysyfe = "0%"; // 原所有份额
            String zfe = "0%"; // 总份额
            // 2、原产权人，继承人配偶
            if (ycqrxxMap.containsKey(jcrposfzhm)) {
                type = NewPropertyOwnerTypeEnum.ORIGINAL.getValue().concat(",").concat(NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue());
                ysyfe = ycqrxxMap.get(jcrposfzhm).getYcqrsyfe();
            } else if (yyxgyrxxMap.containsKey(jcrposfzhm)) {
                type = NewPropertyOwnerTypeEnum.ORIGINAL.getValue().concat(",").concat(NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue());
                ysyfe = yyxgyrxxMap.get(jcrposfzhm).getYyxgyrsyfe();
            } else {
                // 3、继承人配偶
                type = NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue();
            }
            if ("0%".equals(ysyfe) && "0%".equals(JcrpodbfeTotal)) {
                continue;
            }
            zfe = FractionOrPercentageUtil.calculateSum(ysyfe, JcrpodbfeTotal);
            String jcrpodlrxx = jcrxx.getJcrpodlrxx();
            String jcrposfzh = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrsfzhm();
            String jcrposjh = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrsjh();
            String jcrpoxm = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrxm();
            propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(type
                    , jcrxx.getJcrpoxm().concat("（").concat(jcrxx.getJcrxm()).concat("）")
                    , jcrxx.getJcrposfzhm(), jcrxx.getJcrposjh(), ysyfe, "0%", zfe
                    , jcrxx.getJcrposfydlr(), jcrpoxm, jcrposfzh, jcrposjh, jcrxx.getJcrpoqmfs()));


        }

    }

    /**
     * 1、原产权人，继承人  2、原产权人，继承人配偶 3、继承人配偶
     *
     * @param ycqrxxList
     * @param jcrxxList
     * @param propertyOwnerInfoList
     * @param dlrxxMap
     * @param ycqrxx
     * @param sfsyygyr              是否是隐形共有人
     */
    private static void originalInheritanceOwnerSub(List<HomePageFromDataDTO.Ycqrxx> ycqrxxList, List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList
            , List<NewPropertyOwnerInfoVO> propertyOwnerInfoList
            , Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap
            , HomePageFromDataDTO.Ycqrxx ycqrxx
            , boolean sfsyygyr) {
        // 原产权人（原隐形共有人）继承份额 = 原产权人（原隐形共有人）所有份额+继承份额
        String ycqrsyfe = sfsyygyr ? ycqrxx.getYyxgyrsyfe() : ycqrxx.getYcqrsyfe(); // 原所有份额
        List<String> JcrjcfeList = jcrxxList.stream().map(v -> v.getJcrjcfe()).toList();
        String JcrjcfeTotal = FractionOrPercentageUtil.calculateSum(JcrjcfeList.toArray(new String[0])); // 继承份额
        String total = FractionOrPercentageUtil.calculateSum(ycqrsyfe, JcrjcfeTotal); // 总份额
        // 总份额减去继承人配偶登薄份额
        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxpoList = jcrxxList.stream().filter(v -> "已婚".equals(v.getJcrhyzk())).toList();
        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxpodbList = jcrxxpoList.stream().filter(v -> "登簿".equals(v.getJcrposfdb())).toList();
        String JcrpodbfeTotal = "0%";
        if (jcrxxpodbList.size() > 0) {
            List<String> JcrpodbfeList = jcrxxpodbList.stream().map(v -> v.getJcrpodbfe()).toList();
            JcrpodbfeTotal = FractionOrPercentageUtil.calculateSum(JcrpodbfeList.toArray(new String[0])); // 继承人配偶登薄份额
            total = FractionOrPercentageUtil.calculateSubtract(total, JcrpodbfeTotal); // 总份额
        }
        // 保存原产权人（原隐形共有人），继承人
        String dlrxx = sfsyygyr ? ycqrxx.getYyxgyrdlrxx() : ycqrxx.getYcqrdlrxx();
        String dlrsfzh = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrsfzhm();
        String dlrsjh = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrsjh();
        String dlrxm = dlrxxMap.get(dlrxx) == null ? "" : dlrxxMap.get(dlrxx).getDlrxm();
        HeirInfoFillFormDataDTO.Jcrxx jcrxx = jcrxxList.get(0); // 保存原产权人（原隐形共有人）= 继承人信息
        propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(
                NewPropertyOwnerTypeEnum.ORIGINAL.getValue().concat(",").concat(NewPropertyOwnerTypeEnum.INHERITANCE.getValue())
                , jcrxx.getJcrxm(), jcrxx.getJcrsfzhm(), jcrxx.getJcrsjhm(), ycqrsyfe, JcrjcfeTotal, total
                , jcrxx.getJcrsfydlr(), dlrxm, dlrsfzh, dlrsjh, jcrxx.getJcrqmfs()));

        // 继承人配偶
        if (jcrxxpoList.size() == 0) {
            return;
        }
        String jcrposfzhm = jcrxx.getJcrposfzhm();
        // 判断继承人配偶是不是原产权人或者原隐形共有人
        Map<String, HomePageFromDataDTO.Ycqrxx> ycqrxxMap = ycqrxxList.stream().collect(Collectors.toMap(k -> k.getYcqrsfzhm(), v -> v)); // 原产权人信息列表
        Map<String, HomePageFromDataDTO.Ycqrxx> yyxgyrxxMap = ycqrxxList.stream().collect(Collectors.toMap(k -> k.getYyxgyrsfzhm(), v -> v, (v1, v2) -> v1));// 原隐形共有人信息列表
        String type = "";
        String ysyfe = "0%"; // 原所有份额
        String zfe = "0%"; // 总份额
        // 2、原产权人，继承人配偶
        if (ycqrxxMap.containsKey(jcrposfzhm)) {
            type = NewPropertyOwnerTypeEnum.ORIGINAL.getValue().concat(",").concat(NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue());
            ysyfe = ycqrxxMap.get(jcrposfzhm).getYcqrsyfe();
        } else if (yyxgyrxxMap.containsKey(jcrposfzhm)) {
            type = NewPropertyOwnerTypeEnum.ORIGINAL.getValue().concat(",").concat(NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue());
            ysyfe = yyxgyrxxMap.get(jcrposfzhm).getYyxgyrsyfe();
        } else {
            // 3、继承人配偶
            type = NewPropertyOwnerTypeEnum.INHERITANCE_SPOUSE.getValue();
        }
        if ("0%".equals(ysyfe) && "0%".equals(JcrpodbfeTotal)) {
            return;
        }
        zfe = FractionOrPercentageUtil.calculateSum(ysyfe, JcrpodbfeTotal);
        String jcrpodlrxx = jcrxx.getJcrpodlrxx();
        String jcrposfzh = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrsfzhm();
        String jcrposjh = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrsjh();
        String jcrpoxm = dlrxxMap.get(jcrpodlrxx) == null ? "" : dlrxxMap.get(jcrpodlrxx).getDlrxm();
        propertyOwnerInfoList.add(new NewPropertyOwnerInfoVO(type
                , jcrxx.getJcrpoxm().concat("（").concat(jcrxx.getJcrxm()).concat("）")
                , jcrxx.getJcrposfzhm(), jcrxx.getJcrposjh(), ysyfe, "0%", zfe
                , jcrxx.getJcrposfydlr(), jcrpoxm, jcrposfzh, jcrposjh, jcrxx.getJcrpoqmfs()));
    }

    /**
     * 生成放弃登薄人信息列表
     *
     * @param jcrxxList
     * @return
     */
    private List<AbandonDengBoInfoVO> generateAbandonDengBoInfoList(List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxList
            , Map<String, HomePageFromDataDTO.Dlrxx> dlrxxMap) {
        List<AbandonDengBoInfoVO> abandonDengBoInfoList = new ArrayList<>();
        AbandonDengBoInfoVO abandonDengBoInfoVO = null;
        List<HeirInfoFillFormDataDTO.Jcrxx> jcrxxpoList = jcrxxList.stream()
                .filter(v -> "已婚".equals(v.getJcrhyzk()) && "不登簿".equals(v.getJcrposfdb()))
                .collect(Collectors.toMap(k -> k.getJcrposfzhm(), v -> v, (a, b) -> a))
                .values().stream().toList();
        for (HeirInfoFillFormDataDTO.Jcrxx jcrxx : jcrxxpoList) {
            abandonDengBoInfoVO = new AbandonDengBoInfoVO();
            abandonDengBoInfoVO.setXm(jcrxx.getJcrpoxm());
            abandonDengBoInfoVO.setSfzhm(jcrxx.getJcrposfzhm());
            abandonDengBoInfoVO.setSjhm(jcrxx.getJcrposjh());
            abandonDengBoInfoVO.setQmfs(jcrxx.getJcrpoqmfs());
            abandonDengBoInfoVO.setSfwcqz(false);
            abandonDengBoInfoVO.setSfzxqz("线上签名".equals(jcrxx.getJcrpoqmfs()));
            // 代理人信息
            if ("有".equals(jcrxx.getJcrposfydlr())) {
                abandonDengBoInfoVO.setDlrxm(jcrxx.getJcrpodlrxx());
                abandonDengBoInfoVO.setDlrsfzh(jcrxx.getJcrposfzhm());
                abandonDengBoInfoVO.setDlrsjh(jcrxx.getJcrposjh());
            }
            abandonDengBoInfoVO.setSfydlr(jcrxx.getJcrposfydlr());
            abandonDengBoInfoList.add(abandonDengBoInfoVO);
        }
        return abandonDengBoInfoList;
    }


}
