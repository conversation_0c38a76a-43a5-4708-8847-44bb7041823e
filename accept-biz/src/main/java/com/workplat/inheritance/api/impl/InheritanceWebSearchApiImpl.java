package com.workplat.inheritance.api.impl;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.inheritance.InheritanceWebSearchApi;
import com.workplat.inheritance.constant.BizInstanceExtendJsonEnum;
import com.workplat.inheritance.converter.BizInstanceInheritVOConvert;
import com.workplat.inheritance.entity.BizInstanceInherit;
import com.workplat.inheritance.query.BizInstanceInheritQuery;
import com.workplat.inheritance.service.BizInstanceInheritService;
import com.workplat.inheritance.service.BizInstanceInheritSignatureService;
import com.workplat.inheritance.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8
 */
@Slf4j
@Transactional(rollbackFor = Exception.class)
@RestController
public class InheritanceWebSearchApiImpl implements InheritanceWebSearchApi {


    @Autowired
    private BizInstanceInheritService bizInstanceInheritService;
    @Autowired
    private BizInstanceInheritVOConvert bizInstanceInheritVOConvert;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private BizInstanceInheritSignatureService bizInstanceInheritSignatureService;


    @Override
    public ResponseData<Page<BizInstanceInheritVO>> page(BizInstanceInheritQuery query, PageableDTO pageable) {
        Page<BizInstanceInherit> page = bizInstanceInheritService.queryForPage(query, pageable.convertPageable());
        Page<BizInstanceInheritVO> convert = bizInstanceInheritVOConvert.convert(page);
        return ResponseData.success(convert);
    }

    @Override
    public ResponseData<InheritanceSearchDetailsVO> details(String instanceId) {
        InheritanceSearchDetailsVO signatureViewDetailsVO = new InheritanceSearchDetailsVO();
        BizInstanceInfo instanceInfo = bizInstanceInfoService.queryById(instanceId);
        // 不动产登记簿信息
        signatureViewDetailsVO.setRealEstateVO(
                (RealEstateVO) BizInstanceExtendJsonEnum.REAL_ESTATE_VO.convertObject(instanceInfo));
        // 原产权人信息
        signatureViewDetailsVO.setOwnerInfoList(
                (List<OriginalPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo));
        // 新产权人信息
        List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList = (List<NewPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo);
        signatureViewDetailsVO.setNewPropertyOwnerInfoList(newPropertyOwnerInfoVOList);
        // 设置是否签字和是否在线签字
        bizInstanceInheritSignatureService.setSignatureSituation(instanceId, newPropertyOwnerInfoVOList, null);

        signatureViewDetailsVO.setInstanceId(instanceId);
        return ResponseData.success(signatureViewDetailsVO);
    }

    @Override
    public ResponseData<List<BizInstanceWorkFlowVO>> process(String instanceId) {
        List<BizInstanceWorkFlowVO> workFlowVOList = bizInstanceInheritService.getBizInstanceWorkFlowVOList(instanceId);
        return ResponseData.success(workFlowVOList);
    }

    @Override
    public ResponseData<List<FileVO>> viewFile(String instanceId) {
        List<FileVO> fileVOList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            FileVO fileVO = new FileVO();
            fileVO.setFileId("7600e60c015d4f11b1fcfaac64cead7b");
            fileVO.setFileName("测试");
            fileVO.setFileSize(100L);
            fileVO.setFileType("pdf");
            fileVOList.add(fileVO);
        }
        return ResponseData.success(fileVOList);
    }
}
