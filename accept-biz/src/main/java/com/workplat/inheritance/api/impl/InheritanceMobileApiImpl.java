package com.workplat.inheritance.api.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.biz.converter.BizInstanceMaterialConvert;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterial;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.util.DateTimeUtils;
import com.workplat.inheritance.InheritanceMobileApi;
import com.workplat.inheritance.constant.BizInstanceExtendJsonEnum;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import com.workplat.inheritance.entity.BizInstanceInherit;
import com.workplat.inheritance.entity.BizInstanceInheritSignature;
import com.workplat.inheritance.request.PaymentCompleteSubmitRequest;
import com.workplat.inheritance.request.SignatureInstanceRequest;
import com.workplat.inheritance.request.SignatureSubmitRequest;
import com.workplat.inheritance.service.BizInstanceInheritService;
import com.workplat.inheritance.service.BizInstanceInheritSignatureService;
import com.workplat.inheritance.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Slf4j
@Transactional(rollbackFor = Exception.class)
@RestController
public class InheritanceMobileApiImpl implements InheritanceMobileApi {

    @Autowired
    private BizInstanceInheritSignatureService bizInstanceInheritSignatureService;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private BizInstanceMaterialService bizInstanceMaterialService;
    @Autowired
    private BizInstanceMaterialConvert bizInstanceMaterialConvert;
    @Autowired
    private BizInstanceInheritService bizInstanceInheritService;


    @Override
    public ResponseData<List<SignatureInstanceVO>> getSignatureInstance(SignatureInstanceRequest signatureInstanceRequest) {

        // todo 换用户信息
        String token = signatureInstanceRequest.getToken();
        String idNumber = token;

        // 根据身份证获得签字列表
        List<BizInstanceInheritSignature> inheritSignatureList = bizInstanceInheritSignatureService.getSignatureInstanceByZzqzrsfzhm(idNumber);
        List<SignatureInstanceVO> signatureInstanceVOList = new ArrayList<>();
        SignatureInstanceVO signatureInstanceVO = null;
        for (BizInstanceInheritSignature inheritSignature : inheritSignatureList) {
            signatureInstanceVO = new SignatureInstanceVO();
            BizInstanceInfo instance = inheritSignature.getInstance();
            BizInstanceInherit bizInstanceInherit = bizInstanceInheritService.getByInstanceId(instance.getId());
            if (bizInstanceInherit == null) {
                continue;
            }
            // 拿到主页不动产信息
            Object o = instance.extendJsonGet(BizInstanceExtendJsonEnum.REAL_ESTATE_VO.getCode());
            RealEstateVO realEstateVO = JSON.parseObject(String.valueOf(o), RealEstateVO.class);
            signatureInstanceVO.setInstanceId(instance.getId());
            signatureInstanceVO.setNo(instance.getAcceptedNum());
            signatureInstanceVO.setCreateTime(DateTimeUtils.format(instance.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            signatureInstanceVO.setPropertyRightCertificateNo(realEstateVO.getBdcqzh());
            signatureInstanceVO.setHouseAddress(realEstateVO.getFwzl());
            signatureInstanceVO.setBusinessStatus(bizInstanceInherit.getCurrentNode());
            signatureInstanceVOList.add(signatureInstanceVO);
        }

        return ResponseData.success(signatureInstanceVOList);
    }

    @Override
    public ResponseData<SignatureViewDetailsVO> getSignatureViewDetails(String instanceId) {
        SignatureViewDetailsVO signatureViewDetailsVO = new SignatureViewDetailsVO();
        BizInstanceInfo instanceInfo = bizInstanceInfoService.queryById(instanceId);

        // 不动产登记簿信息
        signatureViewDetailsVO.setRealEstateVO(
                (RealEstateVO) BizInstanceExtendJsonEnum.REAL_ESTATE_VO.convertObject(instanceInfo));
        // 原产权人信息
        signatureViewDetailsVO.setOwnerInfoList(
                (List<OriginalPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo));
        // 签名文件预览
        signatureViewDetailsVO.setSignatureFilePreviewList(
                (List<SignatureFilePreviewVO>) BizInstanceExtendJsonEnum.SIGNATURE_FILE_PREVIEW_VO.convertObject(instanceInfo));
        // 新产权人信息
        signatureViewDetailsVO.setNewPropertyOwnerInfoList(
                (List<NewPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo));
        // 放弃登薄人信息
        signatureViewDetailsVO.setAbandonDengBoInfoList(
                (List<AbandonDengBoInfoVO>) BizInstanceExtendJsonEnum.ABANDON_DENG_BO_INFO_VO.convertObject(instanceInfo));
        // 上传材料
        List<BizInstanceMaterial> instanceMaterialList = bizInstanceMaterialService.queryForList(ImmutableMap.of("=(instance.id)", instanceId)
                , Sort.by(Sort.Direction.ASC, "sort"));
        if (CollUtil.isNotEmpty(instanceMaterialList)) {
            List<BizInstanceMaterialVO> bizInstanceMaterialVOList = bizInstanceMaterialConvert.convertAdminFile(instanceMaterialList, false);
            signatureViewDetailsVO.setBizInstanceMaterialVOList(bizInstanceMaterialVOList);
        }

        return ResponseData.success(signatureViewDetailsVO);
    }

    @Override
    public ResponseData<SignatureViewProgressVO> getSignatureViewProgress(String instanceId) {
        SignatureViewProgressVO signatureViewProgressVO = new SignatureViewProgressVO();
        List<BizInstanceWorkFlowVO> workFlowVOList = bizInstanceInheritService.getBizInstanceWorkFlowVOList(instanceId);
        signatureViewProgressVO.setInstanceId(instanceId);
        signatureViewProgressVO.setWorkFlowVOList(workFlowVOList);
        return ResponseData.success(signatureViewProgressVO);
    }

    @Override
    public ResponseData<SignatureDetailsVO> getSignatureDetails(String instanceId) {
        BizInstanceInfo instanceInfo = bizInstanceInfoService.queryById(instanceId);
        SignatureDetailsVO signatureDetailsVO = new SignatureDetailsVO();
        // 确认材料
        signatureDetailsVO.setSignatureFilePreviewList(
                (List<SignatureFilePreviewVO>) BizInstanceExtendJsonEnum.SIGNATURE_FILE_PREVIEW_VO.convertObject(instanceInfo));
        // 原产权人信息
        List<OriginalPropertyOwnerInfoVO> originalPropertyOwnerInfoVOList = (List<OriginalPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.ORIGINAL_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo);
        signatureDetailsVO.setOwnerInfoList(originalPropertyOwnerInfoVOList);
        // 新产权人信息
        List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList = (List<NewPropertyOwnerInfoVO>) BizInstanceExtendJsonEnum.NEW_PROPERTY_OWNER_INFO_VO.convertObject(instanceInfo);
        signatureDetailsVO.setNewPropertyOwnerInfoList(newPropertyOwnerInfoVOList);
        // 放弃登薄人信息
        List<AbandonDengBoInfoVO> abandonDengBoInfoVOList = (List<AbandonDengBoInfoVO>) BizInstanceExtendJsonEnum.ABANDON_DENG_BO_INFO_VO.convertObject(instanceInfo);
        signatureDetailsVO.setAbandonDengBoInfoList(abandonDengBoInfoVOList);

        // 设置是否签字和是否在线签字
        bizInstanceInheritSignatureService.setSignatureSituation(instanceId, newPropertyOwnerInfoVOList, abandonDengBoInfoVOList);

        signatureDetailsVO.setInstanceId(instanceId);
        return ResponseData.success(signatureDetailsVO);
    }

    @Override
    public ResponseData<Void> signatureSubmit(SignatureSubmitRequest submitRequest) throws Exception {
        bizInstanceInheritSignatureService.signatureSubmit(submitRequest.getInstanceId(), submitRequest.getIdNumber()
                , submitRequest.getFile());
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> SignatureDetailsSubmit(String instanceId) {
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<PaymentDetailsVO> getPaymentDetails(String instanceId) {
        PaymentDetailsVO paymentDetailsVO = new PaymentDetailsVO();
        List<PaymentDetailsVO.Materials> confirmMaterialsList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            PaymentDetailsVO.Materials materials = new PaymentDetailsVO.Materials();
            materials.setFileId("测试");
            materials.setFileName("测试");
            materials.setFileSize(100L);
            materials.setFileType("pdf");
            confirmMaterialsList.add(materials);
        }
        paymentDetailsVO.setInstanceId(instanceId);
        paymentDetailsVO.setMaterialsList(confirmMaterialsList);
        return ResponseData.success(paymentDetailsVO);
    }

    @Override
    public ResponseData<Void> PaymentCompleteSubmit(PaymentCompleteSubmitRequest request) {
        String instanceId = request.getInstanceId();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> returnToReSubmit(String instanceId) {
        BizInstanceInherit bizInstanceInherit = bizInstanceInheritService.getByInstanceId(instanceId);
        String currentNode = bizInstanceInherit.getCurrentNode();
        // 税务退回重新提交变为税务审核
        if (InheritanceNodeStatusEnum.TAX_RETURN.name().equals(currentNode)) {
            bizInstanceInheritService.setInheritanceCurrentNode(instanceId, InheritanceNodeStatusEnum.TAX_AUDIT
                    , "退回重新提交");
        } else if (InheritanceNodeStatusEnum.REALESTATE_RETURN.name().equals(currentNode)) {
            // 不动产退回重新提交变为不动产受理
            bizInstanceInheritService.setInheritanceCurrentNode(instanceId, InheritanceNodeStatusEnum.WAIT_REALESTATE_ACCEPTANCE
                    , "退回重新提交");
        }
        return ResponseData.success().build();
    }
}
