package com.workplat.serve.converter;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.entity.BizServeType;
import com.workplat.accept.business.serve.vo.BizServeMethodVo;
import com.workplat.accept.business.serve.vo.BizServeTypeVo;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.springframework.stereotype.Component;


@Component
public class BizServeTypeConverter implements BaseConverter<BizServeType, BizServeTypeVo> {

    @Override
    public BizServeTypeVo convert(BizServeType source) {
        if (source ==null){
            return null;
        }
        BizServeTypeVo bizServeTypeVo=new BizServeTypeVo();
        bizServeTypeVo.setId(source.getId());
        if (source.getMethod() !=null){
            bizServeTypeVo.setMethodId(source.getMethod().getId());
        }
        bizServeTypeVo.setType(source.getType());
        bizServeTypeVo.setIcon(source.getIcon());
        bizServeTypeVo.setDz(source.getDz());
        bizServeTypeVo.setMs(source.getMs());
        return bizServeTypeVo;
    }
}
