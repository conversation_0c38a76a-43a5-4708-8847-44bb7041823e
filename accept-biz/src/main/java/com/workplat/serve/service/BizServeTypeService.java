package com.workplat.serve.service;


import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.entity.BizServeType;
import com.workplat.gss.common.core.service.BaseService;

/**
 * @author: qian cheng
 * @package: com.workplat.serve.service
 * @description: 服务渠道Service
 * @date: 2025/5/14 15:51
 */
public interface BizServeTypeService extends BaseService<BizServeType> {


}
