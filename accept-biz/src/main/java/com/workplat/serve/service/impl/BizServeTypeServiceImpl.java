package com.workplat.serve.service.impl;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.entity.BizServeType;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.serve.service.BizServeMethodService;
import com.workplat.serve.service.BizServeTypeService;
import org.springframework.stereotype.Service;

/**
 * @author: qian cheng
 * @package: com.workplat.serve.service.impl
 * @description: 服务办理方式ServiceImpl
 * @date: 2025/5/14 15:52
 */
@Service
public class BizServeTypeServiceImpl extends BaseServiceImpl<BizServeType> implements BizServeTypeService {



}
