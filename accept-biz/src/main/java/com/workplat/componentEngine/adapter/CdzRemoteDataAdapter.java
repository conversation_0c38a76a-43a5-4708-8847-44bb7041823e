package com.workplat.componentEngine.adapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CDZ专用远程数据适配器
 * 展示如何为特定业务场景创建自定义适配器
 * 
 * <AUTHOR>
 * @date 2025/06/25
 */
@Slf4j
@Component
public class CdzRemoteDataAdapter implements RemoteDataAdapter {
    
    private final DefaultRemoteDataAdapter defaultAdapter;
    
    public CdzRemoteDataAdapter(DefaultRemoteDataAdapter defaultAdapter) {
        this.defaultAdapter = defaultAdapter;
    }
    
    @Override
    public String fetchData(String url, HttpMethod method, HttpHeaders headers, String requestBody, Map<String, Object> params) {
        // 可以在这里添加CDZ特有的请求处理逻辑
        log.info("CDZ适配器处理请求: url={}, method={}", url, method);
        
        // 委托给默认适配器处理
        return defaultAdapter.fetchData(url, method, headers, requestBody ,params);
    }
    
    @Override
    public Object transformData(String responseBody) {
        if (responseBody == null) {
            return null;
        }
        
        // CDZ特有的数据转换逻辑
        try {
            // 假设CDZ返回的数据格式与标准格式略有不同
            JSONObject response = JSON.parseObject(responseBody);
            
            // 如果是CDZ特有格式，进行特殊处理
            if (response.containsKey("cdzData")) {
                return transformCdzData(response.getJSONObject("cdzData"));
            }
            
            // 否则使用默认转换
            return defaultAdapter.transformData(responseBody);
        } catch (Exception e) {
            log.error("CDZ数据转换失败: {}", e.getMessage());
            return defaultAdapter.transformData(responseBody);
        }
    }
    
    @Override
    public JSONObject getConfig(String content, String configKey) {
        // 使用默认的配置解析
        return defaultAdapter.getConfig(content, configKey);
    }
    
    /**
     * 转换CDZ特有的数据格式
     */
    private Object transformCdzData(JSONObject cdzData) {
        // 实现CDZ特有的数据转换逻辑
        // 这里只是示例，实际实现根据具体需求
        log.info("处理CDZ特有数据格式");
        return cdzData;
    }
}
