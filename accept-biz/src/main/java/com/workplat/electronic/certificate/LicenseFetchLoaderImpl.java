//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.util.Sm4Util;
import com.workplat.gss.common.script.model.instance.LicenseFetchInput;
import com.workplat.gss.common.script.model.instance.LicenseFetchOutput;
import com.workplat.gss.common.script.service.instance.LicenseFetchLoader;
import com.workplat.gss.file.constant.SysFileSource;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 电子证照脚本测试模板
 * 这玩意复制到脚本中心的测试里直接就可以执行
 */
@Slf4j
public class LicenseFetchLoaderImpl implements LicenseFetchLoader {

    // 电子证照测试地址
    public static final String URL = "http://***************:8084";

    public static void main(String[] args) {
        LicenseFetchLoader licenseFetchLoader = new LicenseFetchLoaderImpl();
        LicenseFetchOutput output = licenseFetchLoader.licenseFetch(new LicenseFetchInput());
        System.out.println(JSONObject.toJSONString(output, JSONWriter.Feature.PrettyFormat));
    }


    @Override
    public LicenseFetchOutput licenseFetch(LicenseFetchInput input) {
        LicenseFetchOutput output = new LicenseFetchOutput();

        // 表单数据
        JSONObject inputO = JSONObject.from(input);
        JSONObject formData = new JSONObject();
        formData.putAll(inputO);
        formData.put("姓名", "郏伟康");
        formData.put("年龄", 30);

        String body = findCertificateByHolderCodeAndTypeCodes();
        JSONObject data = JSONArray.parseArray(body).getJSONObject(0);
        formData.putAll(data);

        output.setFormData(formData);

        // 上传文件
        try {
            // 获得原文件
            String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("certFileId");
            byte[] bytes = downloadCertificate(body, certFileId);
            SysFileEntity sysFileEntity = getSysFileEntity(bytes, input.getFileName() + "-原件.pdf");

            // 获得文件凭证
            String previewCertFileId = JSONArray.parseArray(body).getJSONObject(0).getString("previewCertFileId");
            byte[] previewBytes = downloadCertificate(body, previewCertFileId);
            SysFileEntity sysFileEntity1 = getSysFileEntity(previewBytes, input.getFileName() + "-凭证.pdf");

            List<JSONObject> fileList = new ArrayList<>();
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(sysFileEntity));
            jsonObject.put("adminFile", true);
            fileList.add(jsonObject);
            // 凭证
            JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(sysFileEntity1));
            fileList.add(jsonObject1);
            // 文件列表
            output.setFiles(fileList);

        } catch (Exception e) {
            throw new BusinessException("获取电子证照数据异常", e);
        }

        return output;
    }

    /**
     * 获得文件信息
     *
     * @param bytes    文件流
     * @param fileName 文件名
     * @return 文件信息
     */
    private static SysFileEntity getSysFileEntity(byte[] bytes, String fileName) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        SysFileUploadModel mongoFileModel = new SysFileUploadModel();
        mongoFileModel.setContent(inputStream);
        mongoFileModel.setFileName(fileName);
        mongoFileModel.setContentType("pdf");
        mongoFileModel.setLength((long) bytes.length);
        mongoFileModel.setFileSource(SysFileSource.SYSTEM_USER_PRIVATE_FILE.name());
        mongoFileModel.setMd5(DigestUtils.md5Hex(inputStream.readAllBytes()));
        SysFileEntityService entityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        return entityService.upload(mongoFileModel);
    }

    /**
     * 获得电子证照数据
     *
     * @return
     */
    public static String findCertificateByHolderCodeAndTypeCodes() {
        String body = HttpRequest.post(URL + "/api/safety/certificate/custom/findCertificateByHolderCodeAndTypeCodes")
                .body(new JSONObject()
                        .fluentPut("holderType", "PERSON")
                        .fluentPut("certificateHolderCode", Sm4Util.encodeStr("320522199208160021", "qvOECaTrKRUgfl4i"))
                        .fluentPut("certificateTypeCodes", ListUtil.of("GA_0121"))
                        .fluentPut("platformCode", "allinone")
                        .fluentPut("usageIp", "*************")
                        .fluentPut("serviceItemCode", "query")
                        .fluentPut("serviceItemName", "查询事项")
                        .fluentPut("queryPersonName", Sm4Util.encodeStr("我不到啊", "qvOECaTrKRUgfl4i"))
                        .fluentPut("queryPersonCardId", Sm4Util.encodeStr("我不到啊", "qvOECaTrKRUgfl4i"))
                        .toJSONString()

                )
                .execute()
                .body();
        if (body.contains("\"status\":400")) {
            throw new BusinessException("获取电子证照数据失败：" + body);
        }

        return body;
    }

    /**
     * 获得电子证照文件
     *
     * @return
     */
    public static byte[] downloadCertificate(String body, String certFileId) {
        //String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("previewCertFileId");
//        String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("certFileId");

        byte[] bytes = HttpRequest.post(URL + "/api/safety/certificate/custom/downloadCertificate")
                .body(new JSONObject()
                        .fluentPut("platformCode", "allinone")
                        .fluentPut("serviceItemCode", "query")
                        .fluentPut("serviceItemName", "查询事项")
                        .fluentPut("usageIp", "*************")
                        .fluentPut("certFileId", certFileId)
                        .toJSONString()
                )
                .execute()
                .bodyBytes();

        return bytes;
    }

}
