package com.workplat.gss.application.biz.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @Title: SignResponseBean
 * @ProjectName jy-ext-service
 * @Description: TODO
 * @date 2021/5/1813:24
 */
public class SignResponseBean {
    Boolean success;
    String message;
    List<String> pdfBase64;

    public SignResponseBean() {
    }

    public SignResponseBean(Boolean success, String message, List<String> pdfBase64) {
        this.success = success;
        this.message = message;
        this.pdfBase64 = pdfBase64;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getPdfBase64() {
        return pdfBase64;
    }

    public void setPdfBase64(List<String> pdfBase64) {
        this.pdfBase64 = pdfBase64;
    }
}
