package com.workplat.gss.application.biz.dto;

/**
 * <AUTHOR>
 * @Title: TcSignBean
 * @ProjectName jy-ext-service
 * @Description: TODO
 * @date 2021/11/2915:59
 */
public class TcSignBean {
    //签名文件
    private String signPdfBase64;
    //签名图片
    private String signImgBase64;
    //自定义图片宽
    private Integer toWidth;
    //自定义图片高
    private Integer toHeight;
    //用户真实姓名
    private String username;
    //用户手机号码
    private String mobile;
    //关键字
    private String keyWord;
    //0表示全部盖章 1表示第一个 2表示最后一个
    private Integer control;
    //cx：x轴偏移量
    //表示向左 -表示向右
    private Float cx;
    //cy：y轴偏移量
    //表示向上 -表示向下
    private Float cy;


    public TcSignBean() {
    }

    public TcSignBean(String signPdfBase64, String signImgBase64, Integer toWidth, Integer toHeight, String username, String mobile, String keyWord, Integer control, Float cx, Float cy) {
        this.signPdfBase64 = signPdfBase64;
        this.signImgBase64 = signImgBase64;
        this.toWidth = toWidth;
        this.toHeight = toHeight;
        this.username = username;
        this.mobile = mobile;
        this.keyWord = keyWord;
        this.control = control;
        this.cx = cx;
        this.cy = cy;
    }

    public String getSignPdfBase64() {
        return signPdfBase64;
    }

    public void setSignPdfBase64(String signPdfBase64) {
        this.signPdfBase64 = signPdfBase64;
    }

    public String getSignImgBase64() {
        return signImgBase64;
    }

    public void setSignImgBase64(String signImgBase64) {
        this.signImgBase64 = signImgBase64;
    }

    public Integer getToWidth() {
        return toWidth;
    }

    public void setToWidth(Integer toWidth) {
        this.toWidth = toWidth;
    }

    public Integer getToHeight() {
        return toHeight;
    }

    public void setToHeight(Integer toHeight) {
        this.toHeight = toHeight;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getControl() {
        return control;
    }

    public void setControl(Integer control) {
        this.control = control;
    }

    public Float getCx() {
        return cx;
    }

    public void setCx(Float cx) {
        this.cx = cx;
    }

    public Float getCy() {
        return cy;
    }

    public void setCy(Float cy) {
        this.cy = cy;
    }
}
