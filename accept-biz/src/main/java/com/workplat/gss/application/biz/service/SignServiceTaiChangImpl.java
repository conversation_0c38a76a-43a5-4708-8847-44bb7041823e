package com.workplat.gss.application.biz.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.workplat.gss.application.biz.dto.SignResponseBean;
import com.workplat.gss.application.biz.dto.TcSignBean;
import com.workplat.gss.common.doc.pdf.dto.MultiSignPdfParam;
import com.workplat.gss.common.doc.pdf.sign.service.AbstractSignService;
import com.workplat.gss.common.doc.pdf.sign.service.SignService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 太仓接口合成签字pdf
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/17 17:26
 */
@Primary
@Service
public class SignServiceTaiChangImpl extends AbstractSignService implements SignService {

    @Value("${sign.url:https://zwfw.taicang.gov.cn/estateNew/api/apiSend/send}")
    private String URL;

    @Override
    protected byte[] doGenerateSignFile(MultiSignPdfParam multiSignPdfParam) throws Exception {
        byte[] bytes = null;
        String signPdfBase64 = multiSignPdfParam.getSignPdfBase64();

        for (MultiSignPdfParam.SignInfo signInfo : multiSignPdfParam.getSignList()) {
            TcSignBean tcSignBean = new TcSignBean();
            tcSignBean.setKeyWord(signInfo.getKeyWord());
            tcSignBean.setToHeight(signInfo.getImageHeight());
            tcSignBean.setToWidth(signInfo.getImageWidth());
            tcSignBean.setControl(0);
            tcSignBean.setUsername("jwk");
            tcSignBean.setMobile("17621860609");
            for (MultiSignPdfParam.ImageInfo imageInfo : signInfo.getImageList()) {
                tcSignBean.setSignPdfBase64(bytes == null ? signPdfBase64 : Base64.encode(bytes));
                tcSignBean.setSignImgBase64(imageInfo.getSignImgBase64());
                tcSignBean.setCx(-imageInfo.getX());
                tcSignBean.setCy(imageInfo.getY());
                bytes = this.getSignFile(tcSignBean);
            }
        }
        return bytes;
    }

    public byte[] getSignFile(TcSignBean tcSignBean) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("apiId", "0223638c0000000074783481ffffdfd0");
        jsonObject.put("getParams", "");
        jsonObject.put("postParams", JSON.toJSONString(tcSignBean));
        HttpResponse httpResponse = HttpRequest.post(URL)
                .body(jsonObject.toString())
                .contentType("application/json;charset=utf-8")
                .execute();
        String responseBody = httpResponse.body();
        SignResponseBean signResponseBean = JSONUtil.toBean(responseBody, SignResponseBean.class);
        if (signResponseBean.getSuccess()) {
            //获取签名文件
            return Base64.decode(signResponseBean.getPdfBase64().get(0));
        } else {
            //再请求一次
            HttpResponse httpResponseRe = HttpRequest.post(URL)
                    .body(jsonObject.toString())
                    .contentType("application/json;charset=utf-8")
                    .execute();
            String responseBodyRe = httpResponseRe.body();
            SignResponseBean signResponseBeanRe = JSONUtil.toBean(responseBodyRe, SignResponseBean.class);
            if (signResponseBeanRe.getSuccess()) {
                //获取签名文件
                return Base64.decode(signResponseBeanRe.getPdfBase64().get(0));
            } else {
                throw new RuntimeException("签字失败！" + signResponseBeanRe.getMessage());
            }
        }
    }

}
