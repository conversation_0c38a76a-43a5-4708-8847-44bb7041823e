package com.workplat.inheritance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 房屋权属查询接口（以权利为主）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/21 14:46
 */
@Getter
@Setter
@Schema(description = "房屋权属查询接口（以权利为主）")
public class RealEstateDTO implements Serializable {

    private Boolean success;
    private String qqsj;
    private Long page;
    private Long size;
    private Long qtime;
    private Long records;
    private Long total;
    private Object cxbh;
    private Object jlid;
    private Long statusCode;
    private String message;
    private Data data;

    @Getter
    @Setter
    @Schema(description = "数据")
    public static class Data implements Serializable {

        @JsonProperty("IsSuccessful")
        private String isSuccessful;

        @JsonProperty("YWBH")
        @Schema(description = "查询编号")
        private String ywbh;

        @Schema(description = "权利信息列表")
        private List<Qllist> qllist;
    }

    @Getter
    @Setter
    @Schema(description = "权利信息列表")
    public static class Qllist implements Serializable {
        /**
         * 序号
         */
        private Long xh;
        /**
         * 权利人
         */
        private String qlr;
        /**
         * 权利人身份证
         */
        private String qlrzjh;
        /**
         * 房屋坐落
         */
        private String fwzl;
        /**
         * 权利标识码
         */
        private String qlbsm;
        /**
         * 合同号
         */
        private String hth;
        /**
         * 查档时间
         */
        private String cdsj;
        /**
         * 登记时间
         */
        private String djsj;
        /**
         * 房屋用途
         */
        private String fwyt;
        /**
         * 土地使用期限
         */
        private String tdsyqx;
        /**
         * 共有方式
         */
        private String gyfs;
        /**
         * 权利类型
         */
        private String qllx;
        /**
         * 权利性质
         */
        private String qlxz;
        /**
         * 权属状态
         */
        private String qszt;
        /**
         * 转移注销时间
         */
        private String zyzxsj;
        /**
         * 不动产类型
         */
        private String bdclx;
        /**
         * 总层数/所在层数
         */
        private String cs;
        /**
         * 宗地面积/建筑面积
         */
        private String mj;
        /**
         * 附记
         */
        private String fj;
        /**
         * 备注
         */
        private String bz;
        /**
         * 限制状态 抵押/查封(限制状态以登记查询窗口为准)
         */
        private String xzzt;
        /**
         * 不动产单元号
         */
        private String bdcdyh;
        /**
         * 不动产权证号
         */
        private String bdcqzh;

        @Schema(description = "房屋信息列表")
        private List<Xxlist> fwxxlist;

        @Schema(description = "土地信息列表")
        private List<Tdxxlist> tdxxlist;

        @Schema(description = "房屋附属设施信息")
        private List<Xxlist> fsssxxlist;

        @Schema(description = "权利人信息列表")
        private List<Qlrxxlist> qlrxxlist;

        @Schema(description = "抵押信息列表")
        private List<Dyaqxxlist> dyaqxxlist;

        @Schema(description = "异议信息列表")
        private List<Yyxxlist> yyxxlist;

        @Schema(description = "查封信息列表")
        private List<Cfxxlist> cfxxlist;

        @Schema(description = "预告信息列表")
        private List<Ygxxlist> ygxxlist;

        private List<Object> dyqxxlist;
        private List<Object> sdxxlist;
        private List<Object> jzqxxlist;

    }

    @Getter
    @Setter
    @Schema(description = "房屋信息列表/房屋附属设施信息")
    public static class Xxlist implements Serializable {
        /**
         * 房屋户室序号
         */
        private String fwhsxh;
        /**
         * 总层数
         */
        private String szc;
        /**
         * 所在层
         */
        private String zcs;
        /**
         * 房屋坐落
         */
        private String fwzl;
        /**
         * 不动产单元号
         */
        private String bdcdyh;
        /**
         * 房屋类型
         */
        private String fwlx;
        /**
         * 不动产类型 土地、房屋
         */
        private String bdclx;
        /**
         * 建筑面积
         */
        private String mj;
        /**
         * 套内建筑面积
         */
        private String tnjzmj;
        /**
         * 分摊建筑面积
         */
        private String ftjzmj;
        /**
         * 分摊土地面积
         */
        private String fttdmj;
        /**
         * 独用土地面积
         */
        private String dytdmj;
        /**
         * 用途
         */
        private String fwyt;
        /**
         * 房屋结构
         */
        private String fwjg;
        /**
         * 土地使用期限
         */
        private String tdsyqx;
        private Date jgsj;
        /**
         * 房屋性质
         */
        private String fwxz;
        private String fjh;
        private String fcdah;
    }

    @Getter
    @Setter
    @Schema(description = "土地信息列表")
    public static class Tdxxlist implements Serializable {

        /**
         * 坐落
         */
        private String fwzl;
        /**
         * 土地证号
         */
        private String tdzh;
        /**
         * 取得方式
         */
        private String qdfs;
        /**
         * 土地使用开始时间
         */
        private String tdsykssj;
        /**
         * 土地使用结束时间
         */
        private String tdsyjssj;
        /**
         * 面积
         */
        private String syqmj;
        /**
         * 用途
         */
        private String yt;
    }

    @Getter
    @Setter
    @Schema(description = "权利人信息列表")
    public static class Qlrxxlist implements Serializable {
        /**
         * 权利人id
         */
        private String qlrid;
        /**
         * 权利人
         */
        private String qlr;
        /**
         * 权利人证件号
         */
        private String qlrzjh;
        /**
         * 产权证号
         */
        private String bdcqzh;
        /**
         * 共有方式
         */
        private String gyfs;
        /**
         * 共有比例
         */
        private String qlbl;
        /**
         * 登簿时间
         */
        private Date dbsj;
        /**
         * 附记
         */
        private String fj;
    }

    @Getter
    @Setter
    @Schema(description = "抵押信息列表")
    public static class Dyaqxxlist implements Serializable {
        /**
         * 抵押权人
         */
        private String dyaqlr;
        /**
         * 不动产证明号（他项证号）
         */
        private String djzmh;
        /**
         * 被担保债权数额
         */
        private String bdbzqse;
        /**
         * 最高债权数额
         */
        private String zgzqse;
        /**
         * 抵押面积
         */
        private String dymj;
        /**
         * 抵押方式
         */
        private String dyfj;
        /**
         * 发证日期
         */
        private String fzrq;
        /**
         * 抵押方式
         */
        private String dyfs;
        /**
         * 债务履行期限（债权确定期间）
         */
        private String zwlxqx;
        /**
         * 债务履行结束时间
         */
        private Date zwlxjssj;
        /**
         * 债务履行开始时间
         */
        private Date zwlxkssj;
        /**
         * 登簿时间
         */
        private String dbsj;
        /**
         * 担保范围
         */
        private String dbfw;
        private String daywh;
        private String sfczdyyd;
        private String sfxzzr;
    }

    @Getter
    @Setter
    @Schema(description = "异议信息列表")
    public static class Yyxxlist implements Serializable {
        /**
         * 异议事项
         */
        private String yysx;
        /**
         * 登簿时间
         */
        private String djsj;
        /**
         * 异议生效期限
         */
        private String qlqx;
        /**
         * 异议证明号
         */
        private Date yydjzmh;
        /**
         * 申请人
         */
        private String sqr;
        /**
         * 附记
         */
        private Date fj;
    }

    @Getter
    @Setter
    @Schema(description = "查封信息列表")
    public static class Cfxxlist implements Serializable {
        /**
         * 查封文号
         */
        private String cfwh;
        /**
         * 查封机关
         */
        private String cfjg;
        /**
         * 查封期限
         */
        private String cfqx;
        /**
         * 查封开始时间
         */
        private Date cfkssj;
        /**
         * 查封结束时间
         */
        private Date cfjssj;
        /**
         * 查封附记
         */
        private String cffj;
        /**
         * 查封范围
         */
        private String cffw;
        /**
         * 查封类型
         */
        private String cflx;
        /**
         * 查封备注
         */
        private Date cxbz;
    }

    @Getter
    @Setter
    @Schema(description = "预告信息列表")
    public static class Ygxxlist implements Serializable {
        /**
         * 预告登记种类
         */
        private String ygdjzl;
        /**
         * 登簿时间
         */
        private Date djsj;
        /**
         * 土地使用开始期限
         */
        private String tdsyksqx;
        /**
         * 土地使用结束期限
         */
        private String tdsyjsqx;
        /**
         * 附记
         */
        private String fj;
    }


}
