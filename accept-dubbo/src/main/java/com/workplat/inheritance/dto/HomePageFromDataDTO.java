package com.workplat.inheritance.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 主页表单FormData对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25
 */
@Getter
@Setter
@Schema(description = "主页表单FormData对象")
public class HomePageFromDataDTO implements Serializable {

    @Schema(description = "代理人列表")
    private List<Dlrxx> dlrxxTable;

    @Schema(description = "原产权人列表")
    private List<Ycqrxx> ycqrxxTable;

    @Schema(description = "份额格式,1百分数|2分数")
    private String fegs;

    @Getter
    @Setter
    @Schema(description = "代理人列表")
    public static class Dlrxx implements Serializable {

        @Schema(description = "代理人姓名")
        private String dlrxm;

        @Schema(description = "代理人身份证号码")
        private String dlrsfzhm;

        @Schema(description = "代理人手机号")
        private String dlrsjh;

    }

    @Getter
    @Setter
    @Schema(description = "原产权人/隐形共有人列表")
    public static class Ycqrxx implements Serializable {

        @Schema(description = "原产权人姓名")
        private String ycqrxm;

        @Schema(description = "原产权人当前生命状态（1在世/2死亡）")
        private String ycqrdqsmzt;

        @Schema(description = "原产权人身份证号码")
        private String ycqrsfzhm;

        @Schema(description = "原产权人所有份额")
        private String ycqrsyfe;

        @Schema(description = "原产权人手机号码")
        private String ycqrsjhm;

        @Schema(description = "原产权人是否继承逝者份额，1继承|2不继承")
        private String ycqrsfjcszfe;

        @Schema(description = "原产权人是否有代理人，1有|2无")
        private String ycqrsfydlr;

        @Schema(description = "原产权人代理人信息")
        private String ycqrdlrxx;

        @Schema(description = "原产权人签名方式，1现场签名|2线上签名")
        private String ycqrqmfs;

        @Schema(description = "原产权人是否有隐形共有人，1有|2无")
        private String sfyyxgyr;


        @Schema(description = "原隐形共有人姓名")
        private String yyxgyrxm;

        @Schema(description = "原隐形共有人当前生命状态（1在世/2死亡）")
        private String yyxgyrdqsmzt;

        @Schema(description = "原隐形共有人身份证号码")
        private String yyxgyrsfzhm;

        @Schema(description = "原隐形共有人所有份额")
        private String yyxgyrsyfe;

        @Schema(description = "原隐形共有人手机号码")
        private String yyxgyrsjhm;

        @Schema(description = "原隐形共有人是否继承逝者份额，1继承|2不继承")
        private String yyxgyrsfjcszfe;

        @Schema(description = "原隐形共有人是否有代理人，1有|2无")
        private String yyxgyrsfydlr;

        @Schema(description = "原隐形共有人代理人信息")
        private String yyxgyrdlrxx;

        @Schema(description = "原隐形共有人签名方式，1现场签名|2线上签名")
        private String yyxgyrqmfs;


    }


}
