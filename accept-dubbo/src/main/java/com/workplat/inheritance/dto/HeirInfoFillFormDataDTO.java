package com.workplat.inheritance.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 继承人信息填写FormData对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/27
 */
@Getter
@Setter
@Schema(description = "继承人信息填写FormData对象")
public class HeirInfoFillFormDataDTO implements Serializable {

    @Schema(description = "新不动产权证共有方式,1共同共有|2按份共有|3单独所有")
    private String xbdcqzgyfs;

    @Schema(description = "份额格式,1百分数|2分数")
    private String fegs;

    @Schema(description = "被继承人信息列表")
    private List<Bjcrxx> bjcrxxTable;


    @Getter
    @Setter
    @Schema(description = "被继承人信息")
    public static class Bjcrxx implements Serializable {

        @Schema(description = "被继承人姓名")
        private String bjcrxm;

        @Schema(description = "被继承人身份证号码")
        private String bjcrsfzhm;

        @Schema(description = "被继承人当前生命状态（1在世/2死亡）")
        private String bjcrdqsmzt;

        @Schema(description = "被继承人所有份额")
        private String bjcrsyfe;

        @Schema(description = "原隐形共有人身份证号码")
        private String yyxgyrsfzhm;

        @Schema(description = "继承人信息列表")
        private List<Jcrxx> jcrxxTable;

    }

    @Getter
    @Setter
    @Schema(description = "继承人信息列表")
    public static class Jcrxx implements Serializable {

        @Schema(description = "继承人姓名")
        private String jcrxm;

        @Schema(description = "继承人身份证号码")
        private String jcrsfzhm;

        @Schema(description = "继承人手机号码")
        private String jcrsjhm;

        @Schema(description = "继承人继承份额")
        private String jcrjcfe;

        @Schema(description = "继承人是否有代理人，1有|2无")
        private String jcrsfydlr;

        @Schema(description = "继承人代理人信息")
        private String jcrdlrxx;

        @Schema(description = "继承人签名方式，1现场签名|2线上签名,继承人必须现场签字")
        private String jcrqmfs = "现场签名";

        @Schema(description = "继承人婚姻状况，1未婚|2已婚|3离异|4丧偶")
        private String jcrhyzk;

        @Schema(description = "继承人是否是隐形共有人，是|否")
        private String jcrsfsyyxgyr;

        @Schema(description = "继承人是否手动新增，是|否")
        private String jcrsfsdxz;


        @Schema(description = "继承人配偶姓名")
        private String jcrpoxm;

        @Schema(description = "继承人配偶身份证号码")
        private String jcrposfzhm;

        @Schema(description = "继承人配偶手机号")
        private String jcrposjh;

        @Schema(description = "继承人配偶是否登薄，登簿|不登簿")
        private String jcrposfdb;

        @Schema(description = "继承人配偶登薄份额")
        private String jcrpodbfe;

        @Schema(description = "继承人配偶签名方式，1现场签名|2线上签名")
        private String jcrpoqmfs;

        @Schema(description = "继承人配偶是否有代理人，1有|2无")
        private String jcrposfydlr;

        @Schema(description = "继承人配偶代理人信息")
        private String jcrpodlrxx;


    }

}
