package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 发起流程提交
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Getter
@Setter
@Schema(description = "发起流程提交")
public class StartProcessSubmitRequest implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;


}
