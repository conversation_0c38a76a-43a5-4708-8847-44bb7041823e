package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/21 14:46
 */
@Getter
@Setter
@Schema(description = "不动产登记簿查询")
public class RealEstateRequest {

    @Schema(description = "产权人姓名")
    @NotBlank(message = "产权人姓名不能为空")
    private String propertyRightName;

    @Schema(description = "产权证号")
    @NotBlank(message = "产权证号不能为空")
    private String propertyRightCertificateNo;
}
