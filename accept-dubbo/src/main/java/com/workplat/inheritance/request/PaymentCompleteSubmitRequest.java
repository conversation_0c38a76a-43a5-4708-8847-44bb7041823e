package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/7 16:27
 */
@Setter
@Getter
@Schema(description = "缴费完成提交")
public class PaymentCompleteSubmitRequest implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "领证信息-暂时不确定怎么传,不确定可以暂时不开发这个")
    private ReceiveCertificateInfo receiveCertificateInfo;


    @Getter
    @Setter
    @Schema(description = "领证信息")
    public static class ReceiveCertificateInfo {

        /**
         * @see com.workplat.inheritance.constant.ReceiveCertificateWayEnum
         */
        @Schema(description = "领证方式")
        private String type;

        @Schema(description = "领证人姓名")
        private String name;

        @Schema(description = "领证人身份证号码")
        private String identityCard;

        @Schema(description = "领证人联系方式")
        private String contactWay;


    }

}
