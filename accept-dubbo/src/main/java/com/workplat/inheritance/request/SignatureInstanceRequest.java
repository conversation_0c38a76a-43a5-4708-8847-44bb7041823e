package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 获得签字办件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Getter
@Setter
@Schema(description = "获得签字办件")
public class SignatureInstanceRequest implements Serializable {

    @Schema(description = "token,测试阶段随便传")
    private String token;


}
