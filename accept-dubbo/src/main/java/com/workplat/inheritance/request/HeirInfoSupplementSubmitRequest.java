package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/5
 */
@Getter
@Setter
@Schema(description = "继承人信息补充提交")
public class HeirInfoSupplementSubmitRequest implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;



}
