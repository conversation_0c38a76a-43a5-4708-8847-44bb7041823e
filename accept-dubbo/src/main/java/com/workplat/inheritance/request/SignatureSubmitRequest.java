package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/13 13:52
 */
@Schema(description = "签字提交")
@Getter
@Setter
public class SignatureSubmitRequest {

    @Schema(description = "办件id")
    private String instanceId;
    @Schema(description = "身份证号码")
    private String idNumber;
    @Schema(description = "签字图片,base64")
    private String file;
}
