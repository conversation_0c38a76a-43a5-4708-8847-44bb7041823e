package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 主页表单提交
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/26 14:46
 */
@Getter
@Setter
@Schema(description = "主页表单提交")
public class HomePageFromSubmitRequest implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "表单数据")
    private String formData;


}
