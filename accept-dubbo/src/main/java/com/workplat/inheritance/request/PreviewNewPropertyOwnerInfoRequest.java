package com.workplat.inheritance.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 预览新的产权人信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/3
 */
@Getter
@Setter
@Schema(description = "预览新的产权人信息")
public class PreviewNewPropertyOwnerInfoRequest implements Serializable {


    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "表单数据")
    private String formData;


}
