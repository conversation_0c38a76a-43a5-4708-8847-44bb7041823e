package com.workplat.inheritance.entity;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.common.core.entity.BaseEntity;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import com.workplat.inheritance.constant.InheritanceStagePageEnum;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

/**
 * 继承公证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8
 */
@Setter
@Getter
@Entity
@Table(name = "biz_instance_inherit")
public class BizInstanceInherit extends BaseEntity {

    @OneToOne
    @JoinColumn(name = "instance_id")
    @Comment("申报对象")
    @Where(clause = "deleted = 0")
    private BizInstanceInfo instance;

    @Column(name = "ycqrxm")
    @Comment("原产权人姓名")
    private String ycqrxm;

    @Column(name = "zl")
    @Comment("坐落")
    private String zl;

    /**
     * @see InheritanceNodeStatusEnum
     */
    @Column(name = "current_node")
    @Comment("当前节点")
    private String currentNode;

    @Column(name = "current_node_name")
    @Comment("当前节点名称")
    private String currentNodeName;

    /**
     * @see InheritanceStagePageEnum
     */
    @Column(name = "stage_page")
    @Comment("当前暂存停留界面")
    private String stagePage;


}
