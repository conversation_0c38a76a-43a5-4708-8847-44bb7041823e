package com.workplat.inheritance.entity;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.common.core.entity.BaseEntity;
import com.workplat.inheritance.constant.NewPropertyOwnerTypeEnum;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * 申报继承公证签字
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Setter
@Getter
@Entity
@Table(name = "biz_instance_inherit_signature")
public class BizInstanceInheritSignature extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "instance_id")
    @Comment("申报对象")
    private BizInstanceInfo instance;

    @Column(name = "sfsfqdbr")
    @Comment("是否是放弃登薄人")
    private Boolean sfsfqdbr;

    /**
     * @see NewPropertyOwnerTypeEnum
     */
    @Column(name = "type")
    @Comment("类型")
    private String type;

    @Column(name = "xm")
    @Comment("姓名")
    private String xm;

    @Column(name = "sfzhm")
    @Comment("身份证号码")
    private String sfzhm;

    @Column(name = "sjhm")
    @Comment("手机号码")
    private String sjhm;

    @Column(name = "ysyfe")
    @Comment("原所有份额")
    private String ysyfe;

    @Column(name = "jcfe")
    @Comment("继承份额")
    private String jcfe;

    @Column(name = "zfe")
    @Comment("总份额")
    private String zfe;

    @Column(name = "sfydlr")
    @Comment("是否有代理人，1有|2无")
    private String sfydlr;

    @Column(name = "dlrxm")
    @Comment("代理人姓名")
    private String dlrxm;

    @Column(name = "dlrsfzh")
    @Comment("代理人身份证号码")
    private String dlrsfzh;

    @Column(name = "dlrsjh")
    @Comment("代理人手机号")
    private String dlrsjh;

    @Column(name = "zzqzrsfzhm")
    @Comment("最终签字人身份证号码，如果有代理人就是代理人身份证")
    private String zzqzrsfzhm;

    @Column(name = "qmfs")
    @Comment("签名方式，现场签名|线上签名")
    private String qmfs;

    @Column(name = "sfwcqz", columnDefinition = "int default 0")
    @Comment("是否完成签字")
    private boolean sfwcqz;

    @Column(name = "sfzxqz", columnDefinition = "int default 0")
    @Comment("是否在线签字")
    private boolean sfzxqz;

    @Column(name = "signature_file_id")
    @Comment("签字文件id")
    private String signatureFileId;


}
