package com.workplat.inheritance.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 继承公证事项
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/24 14:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ConfMatterCodeEnum {

    MAIN("继承公证联办", "JCGZLB"),
    HOME_PAGE("继承公证主页", "TCJCGZ"),
    HEIR_INFO_FILL_IN("继承公证继承人信息填写", "TCJCGZJCRTX");

    private String value;
    private String code;
}
