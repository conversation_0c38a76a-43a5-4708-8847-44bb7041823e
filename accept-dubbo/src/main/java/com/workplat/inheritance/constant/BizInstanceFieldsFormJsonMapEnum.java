package com.workplat.inheritance.constant;

import com.workplat.inheritance.dto.HeirInfoFillFormDataDTO;
import com.workplat.inheritance.dto.HomePageFromDataDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * BizInstanceFields FormJsonMap
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/26 14:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BizInstanceFieldsFormJsonMapEnum {

    HOME_PAGE_FROM_JSON("主页提交表单数据", "homePageFromJson", HomePageFromDataDTO.class),
    HEIR_INFO_FILL_FROM_JSON("继承公证继承人信息填写提交表单数据", "heirInfoFillFromJson", HeirInfoFillFormDataDTO.class);

    private String value;
    private String code;
    private Class aClass;
}
