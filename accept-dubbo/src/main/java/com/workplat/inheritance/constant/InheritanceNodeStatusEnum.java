package com.workplat.inheritance.constant;

import com.workplat.gss.common.core.annotation.dict.DictCode;
import com.workplat.gss.common.core.annotation.dict.DictGroup;
import com.workplat.gss.common.core.annotation.dict.DictName;
import com.workplat.gss.common.core.annotation.dict.DictSort;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务流程
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/7
 */
@AllArgsConstructor
@Getter
@DictGroup(group = "BUSINESS_CUSTCOM", name = "继承公证办件节点状态", code = "INHERITANCE_NODE_STATUS_ENUM")
public enum InheritanceNodeStatusEnum {

    @DictCode
    PRE_ACCEPTANCE("预受理", 1),
    @DictCode
    TAX_AUDIT("税务审核", 2),
    @DictCode
    TAX_RETURN("税务退回", 3),
    @DictCode
    CONFIRM_PAYMENT("确认缴费", 4),
    @DictCode
    WAIT_REALESTATE_ACCEPTANCE("待不动产受理", 5),
    @DictCode
    REALESTATE_RETURN("不动产退回", 6),
    @DictCode
    REALESTATE_ACCEPTANCE("不动产受理", 7),
    @DictCode
    CERTIFICATE("缮证", 8),
    @DictCode
    COMPLETE("办结", 9);

    @DictName
    private String value;
    @DictSort
    private Integer sort;

    /**
     * 根据值获得枚举类型 switch
     *
     * @param value
     * @return
     */
    public static InheritanceNodeStatusEnum getByValue(String value) {
        for (InheritanceNodeStatusEnum code : values()) {
            if (code.getValue().equals(value)) {
                return code;
            }
        }
        return null;
    }

}

