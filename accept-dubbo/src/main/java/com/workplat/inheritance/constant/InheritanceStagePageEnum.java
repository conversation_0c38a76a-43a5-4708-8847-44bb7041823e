package com.workplat.inheritance.constant;

import com.workplat.gss.common.core.annotation.dict.DictCode;
import com.workplat.gss.common.core.annotation.dict.DictGroup;
import com.workplat.gss.common.core.annotation.dict.DictName;
import com.workplat.gss.common.core.annotation.dict.DictSort;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 继承公证申报暂存当前停留界面
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/11
 */
@AllArgsConstructor
@Getter
@DictGroup(group = "BUSINESS_CUSTCOM", name = "继承公证申报暂存当前停留界面", code = "INHERITANCE_STAGE_PAGE_ENUM")
public enum InheritanceStagePageEnum {

    @DictCode
    HOME_PAGE("主页", 1),
    @DictCode
    HEIR_FILLIN_PAGE("继承人信息填写", 2),
    @DictCode
    TAX_RETURN("继承人信息补充", 3),
    @DictCode
    CONFIRM_PAYMENT("上传材料", 4),
    @DictCode
    END("结束", 5);

    @DictName
    private String value;
    @DictSort
    private Integer sort;

    /**
     * 根据值获得枚举类型 switch
     *
     * @param value
     * @return
     */
    public static InheritanceStagePageEnum getByValue(String value) {
        for (InheritanceStagePageEnum code : values()) {
            if (code.getValue().equals(value)) {
                return code;
            }
        }
        return null;
    }

}

