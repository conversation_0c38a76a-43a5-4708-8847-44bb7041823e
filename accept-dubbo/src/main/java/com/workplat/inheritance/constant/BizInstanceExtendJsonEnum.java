package com.workplat.inheritance.constant;

import com.alibaba.fastjson2.JSON;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.inheritance.dto.RealEstateDTO;
import com.workplat.inheritance.vo.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * instance扩展字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/24 14:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BizInstanceExtendJsonEnum {

    //    REAL_ESTATE_QUERY("不动产产权人信息", "REAL_ESTATE_QUERY", RealEstateRequest.class),
    REAL_ESTATE_DTO("不动产信息全部", "REAL_ESTATE_DTO", RealEstateDTO.class),
    REAL_ESTATE_VO("不动产信息简略", "REAL_ESTATE_VO", RealEstateVO.class),
    ORIGINAL_PROPERTY_OWNER_INFO_VO("原产权人信息", "ORIGINAL_PROPERTY_OWNER_INFO_VO", OriginalPropertyOwnerInfoVO.class),
    SIGNATURE_FILE_PREVIEW_VO("签名文件预览", "SIGNATURE_FILE_PREVIEW_VO", SignatureFilePreviewVO.class),
    NEW_PROPERTY_OWNER_INFO_VO("新的产权人信息", "NEW_PROPERTY_OWNER_INFO_VO", NewPropertyOwnerInfoVO.class),
    ABANDON_DENG_BO_INFO_VO("放弃登薄人信息", "ABANDON_DENG_BO_INFO_VO", AbandonDengBoInfoVO.class);
//    HEIR_INFO_SUPPLEMENT("继承人信息补充","HEIR_INFO_SUPPLEMENT",HeirInfoSupplementVO .class);

    private String value;
    private String code;
    private Class aClass;

    public Object convertObject(BizInstanceInfo bizInstanceInfo) {
        String s = String.valueOf(bizInstanceInfo.extendJsonGet(this.getCode()));
        if (s.startsWith("{")) {
            return JSON.parseObject(s, this.getAClass());
        } else {
            return JSON.parseArray(s, this.getAClass());
        }
    }
}
