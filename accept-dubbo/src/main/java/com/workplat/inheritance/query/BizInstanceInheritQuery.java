package com.workplat.inheritance.query;

import com.workplat.gss.common.core.annotation.QueryMapBuild;
import com.workplat.gss.common.core.constant.QueryOperator;
import com.workplat.gss.common.core.dto.BaseQuery;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 申报继承公证
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Setter
@Getter
public class BizInstanceInheritQuery extends BaseQuery {

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.acceptedNum")
    @Schema(description = "业务编号")
    private String acceptedNum;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "ycqrxm")
    @Schema(description = "原产权人姓名")
    private String ycqrxm;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "zl")
    @Schema(description = "坐落")
    private String zl;

    /**
     * @see InheritanceNodeStatusEnum
     */
    @QueryMapBuild(operator = QueryOperator.EQUAL, fieldName = "currentNode")
    @Schema(description = "当前节点")
    private String currentNode;


}
