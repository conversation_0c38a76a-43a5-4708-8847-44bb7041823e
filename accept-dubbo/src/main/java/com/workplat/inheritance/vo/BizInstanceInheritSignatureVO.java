package com.workplat.inheritance.vo;

import com.workplat.inheritance.constant.NewPropertyOwnerTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 申报继承公证签字VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Setter
@Getter
public class BizInstanceInheritSignatureVO{

    @Schema(description = "申报对象")
    private String instanceId;

    @Schema(description = "是否是放弃登薄人")
    private Boolean sfsfqdbr;

    /**
     * @see NewPropertyOwnerTypeEnum
     */
    @Schema(description = "类型")
    private String type;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "身份证号码")
    private String sfzhm;

    @Schema(description = "手机号码")
    private String sjhm;

    @Schema(description = "原所有份额")
    private String ysyfe;

    @Schema(description = "继承份额")
    private String jcfe;

    @Schema(description = "总份额")
    private String zfe;

    @Schema(description = "是否有代理人，1有|2无")
    private String ycqrsfydlr;

    @Schema(description = "代理人姓名")
    private String dlrxm;

    @Schema(description = "代理人身份证号码")
    private String dlrsfzhm;

    @Schema(description = "代理人手机号")
    private String dlrsjh;

    @Schema(description = "最终签字人身份证号码，如果有代理人就是代理人身份证")
    private String zzqzrsfzhm;


}
