package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:28
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "签名文件预览VO")
public class SignatureFilePreviewVO implements Serializable {

    @Schema(description = "序号")
    private String sn;

    @Schema(description = "签名文件")
    private String name;

    @Schema(description = "文件id")
    private String fileId;

    @Schema(description = "文件大小")
    private Long fileSize;
}
