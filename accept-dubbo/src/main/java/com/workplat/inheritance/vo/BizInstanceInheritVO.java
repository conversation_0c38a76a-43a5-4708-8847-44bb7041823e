package com.workplat.inheritance.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.workplat.gss.common.core.constant.DateFormat;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 申报继承公证VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Setter
@Getter
public class BizInstanceInheritVO {

    @Schema(description = "申报对象")
    private String instanceId;

    @Schema(description = "业务编号")
    private String acceptedNum;

    @Schema(description = "原产权人姓名")
    private String ycqrxm;

    @Schema(description = "坐落")
    private String zl;

    @JSONField(format = DateFormat.DATE_TIME_PATTERN)
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * @see InheritanceNodeStatusEnum
     */
    @Schema(description = "当前节点")
    private String currentNode;

    @Schema(description = "当前暂存停留界面")
    private String stagePage;


}
