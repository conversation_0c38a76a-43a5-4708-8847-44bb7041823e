package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/7 15:07
 */

@Getter
@Setter
@Schema(description = "查看签字详情VO")
public class SignatureDetailsVO {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "确认材料")
    private List<SignatureFilePreviewVO> signatureFilePreviewList;

    @Schema(description = "原产权人信息")
    private List<OriginalPropertyOwnerInfoVO> ownerInfoList;

    @Schema(description = "新的产权人信息")
    private List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoList;

    @Schema(description = "放弃登薄人信息")
    private List<AbandonDengBoInfoVO> abandonDengBoInfoList;


}
