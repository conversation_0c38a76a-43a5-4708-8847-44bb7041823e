package com.workplat.inheritance.vo;

import com.workplat.inheritance.constant.NewPropertyOwnerTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:28
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "新的产权人信息")
public class NewPropertyOwnerInfoVO implements Serializable {

    /**
     * @see NewPropertyOwnerTypeEnum
     */
    @Schema(description = "类型")
    private String type;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "身份证号码")
    private String sfzhm;

    @Schema(description = "手机号码")
    private String sjhm;

    @Schema(description = "原所有份额")
    private String ysyfe;

    @Schema(description = "继承份额")
    private String jcfe;

    @Schema(description = "总份额")
    private String zfe;

    @Schema(description = "是否有代理人，1有|2无")
    private String sfydlr;

    @Schema(description = "代理人姓名")
    private String dlrxm;

    @Schema(description = "代理人身份证号")
    private String dlrsfzh;

    @Schema(description = "代理人手机号")
    private String dlrsjh;

    @Schema(description = "签名方式，现场签名|线上签名")
    private String qmfs;

    @Schema(description = "是否完成签字")
    private boolean sfwcqz;

    @Schema(description = "是否在线签字")
    private boolean sfzxqz;

    @Schema(description = "签字文件id")
    private String signatureFileId;

    public NewPropertyOwnerInfoVO(String type, String xm, String sfzhm, String sjhm, String ysyfe, String jcfe, String zfe
            , String sfydlr, String dlrxm, String dlrsfzh, String dlrsjh, String qmfs) {
        this.type = type;
        this.xm = xm;
        this.sfzhm = sfzhm;
        this.sjhm = sjhm;
        this.ysyfe = ysyfe;
        this.jcfe = jcfe;
        this.zfe = zfe;
        this.sfydlr = sfydlr;
        this.dlrxm = dlrxm;
        this.dlrsfzh = dlrsfzh;
        this.dlrsjh = dlrsjh;
        this.qmfs = qmfs;
        sfzxqz = "线上签名".equals(qmfs);
    }
}
