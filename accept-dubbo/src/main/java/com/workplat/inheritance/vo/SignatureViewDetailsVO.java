package com.workplat.inheritance.vo;

import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:07
 */

@Getter
@Setter
@Schema(description = "查看详情VO")
public class SignatureViewDetailsVO {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "不动产登记簿信息")
    private RealEstateVO realEstateVO;

    @Schema(description = "原产权人信息")
    private List<OriginalPropertyOwnerInfoVO> ownerInfoList;

    @Schema(description = "确认材料")
    private List<SignatureFilePreviewVO> signatureFilePreviewList;

    @Schema(description = "新的产权人信息")
    private List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoList;

    @Schema(description = "放弃登薄人信息")
    private List<AbandonDengBoInfoVO> abandonDengBoInfoList;

    @Schema(description = "上传材料")
    private List<BizInstanceMaterialVO> bizInstanceMaterialVOList;

}
