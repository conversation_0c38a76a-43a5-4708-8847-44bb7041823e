package com.workplat.inheritance.vo;

import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:07
 */

@Getter
@Setter
@Schema(description = "查看进度VO")
public class SignatureViewProgressVO {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "办件流程")
    List<BizInstanceWorkFlowVO> workFlowVOList;

}
