package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 不动产登记簿查询VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/21 14:46
 */
@Getter
@Setter
@Schema(description = "不动产登记簿查询VO")
public class RealEstateVO implements Serializable {

    @Schema(description = "原不动产产权证号")
    private String bdcqzh;

    @Schema(description = "幢号")
    private String zh;

    @Schema(description = "房屋坐落")
    private String fwzl;

    @Schema(description = "总层数")
    private String zcs;

    @Schema(description = "所在层数")
    private String szc;

    @Schema(description = "建筑面积")
    private String mj;

    @Schema(description = "建筑结构")
    private String fwjg;

    @Schema(description = "房屋用途")
    private String fwyt;

    @Schema(description = "土地用途")
    private String tdyt;

    @Schema(description = "土地使用面积")
    private String tdsymj;

    @Schema(description = "权利类型")
    private String qllx;

    @Schema(description = "权力性质")
    private String qlxz;

    @Schema(description = "土地使用起始时间")
    private String tdsykssj;

    @Schema(description = "土地使用结束时间")
    private String tdsyjssj;

    @Schema(description = "附记")
    private String fj;

    @Schema(description = "共用方式")
    private String gyfs;

    @Schema(description = "房屋抵押情况(不存在抵押/抵押一次/多次抵押)")
    private String fwdyqk;

    @Schema(description = "房屋查封情况(存在查封情况/不存在查封情况)")
    private String fwcfqk;

    @Schema(description = "文件地址")
    private String fileId;

    @Schema(description = "查询时间")
    private String queryDate;


    // 扩展
    @Schema(description = "房屋性质")
    private String fwxz;
    @Schema(description = "不动产单元号")
    private String bdcdyh;


}
