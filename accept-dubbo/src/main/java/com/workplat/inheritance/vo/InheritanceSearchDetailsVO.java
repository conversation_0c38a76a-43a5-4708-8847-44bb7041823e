package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8 13:44
 */
@Getter
@Setter
@Schema(description = "详情VO")
public class InheritanceSearchDetailsVO {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "不动产登记簿信息")
    private RealEstateVO realEstateVO;

    @Schema(description = "原产权人信息")
    private List<OriginalPropertyOwnerInfoVO> ownerInfoList;

    @Schema(description = "新的产权人信息")
    private List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoList;
}
