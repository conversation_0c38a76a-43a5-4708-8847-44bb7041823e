package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 获得签字办件VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6
 */
@Getter
@Setter
@Schema(description = "获得签字办件VO")
public class SignatureInstanceVO implements Serializable {

    @Schema(description = "办件id，用这个查详情，进度，产权人信息")
    private String instanceId;

    @Schema(description = "办件编号")
    private String no;

    @Schema(description = "创建日期")
    private String createTime;

    @Schema(description = "产权证号")
    private String propertyRightCertificateNo;

    @Schema(description = "房屋地址")
    private String houseAddress;

    @Schema(description = "业务状态")
    private String businessStatus;


}
