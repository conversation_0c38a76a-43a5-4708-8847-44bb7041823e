package com.workplat.inheritance.vo;

import com.workplat.inheritance.constant.NewPropertyOwnerTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 预览新的产权人信息VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/3
 */
@Getter
@Setter
@Schema(description = "预览新的产权人信息VO")
public class PreviewNewPropertyOwnerInfoVO implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "新的产权人信息列表")
    private List<NewPropertyOwnerInfoVO> propertyOwnerInfoList;

    @Schema(description = "放弃登薄人信息列表")
    private List<AbandonDengBoInfoVO> abandonDengBoInfoList;


}
