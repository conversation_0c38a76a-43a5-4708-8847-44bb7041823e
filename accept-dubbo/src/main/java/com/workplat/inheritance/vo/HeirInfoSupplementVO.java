package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 继承人信息补充VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/5
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "继承人信息补充VO")
public class HeirInfoSupplementVO implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "原产权人信息")
    private List<OriginalPropertyOwnerInfoVO> ownerInfoList;

    @Schema(description = "签名文件预览")
    private List<SignatureFilePreviewVO> signatureFilePreviewList;

    @Schema(description = "新的产权人信息")
    private List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoList;

    @Schema(description = "放弃登薄人信息")
    private List<AbandonDengBoInfoVO> abandonDengBoInfoList;


}
