package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 继承人信息填写表单VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/25
 */
@Getter
@Setter
@Schema(description = "继承人信息填写表单VO")
public class HeirInfoFillFormVO implements Serializable {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "表单json")
    private String formJson;

    @Schema(description = "表单数据")
    private String formData;

    @Schema(description = "主页表单数据")
    private String homePageFormData;


}
