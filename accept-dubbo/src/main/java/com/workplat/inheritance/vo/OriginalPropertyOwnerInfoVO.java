package com.workplat.inheritance.vo;

import com.workplat.inheritance.dto.HomePageFromDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:28
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "原产权人信息")
public class OriginalPropertyOwnerInfoVO implements Serializable {

    @Schema(description = "是否隐形共有人")
    private boolean sfycgyr;

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "身份证号码")
    private String sfzhm;

    @Schema(description = "当前生命状态")
    private String dqsmzt;

    @Schema(description = "所有份额")
    private String syfe;

    @Schema(description = "原产权人信息(完全体)")
    private HomePageFromDataDTO.Ycqrxx ycqrxx;

}
