package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/6 15:39
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Schema(description = "放弃登薄人信息")
public class AbandonDengBoInfoVO {

    @Schema(description = "姓名")
    private String xm;

    @Schema(description = "身份证号码")
    private String sfzhm;

    @Schema(description = "手机号码")
    private String sjhm;

    @Schema(description = "是否有代理人，1有|2无")
    private String sfydlr;

    @Schema(description = "代理人姓名")
    private String dlrxm;

    @Schema(description = "代理人身份证号")
    private String dlrsfzh;

    @Schema(description = "代理人手机号")
    private String dlrsjh;

    @Schema(description = "签名方式，现场签名|线上签名")
    private String qmfs;

    @Schema(description = "是否完成签字")
    private boolean sfwcqz;

    @Schema(description = "是否在线签字")
    private boolean sfzxqz;

    @Schema(description = "签字文件id")
    private String signatureFileId;


}
