package com.workplat.inheritance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/7 15:07
 */

@Getter
@Setter
@Schema(description = "查看缴费详情VO")
public class PaymentDetailsVO {

    @Schema(description = "办件id")
    private String instanceId;

    @Schema(description = "税务材料预览")
    private List<Materials> materialsList;

    @Getter
    @Setter
    @Schema(description = "税务材料预览")
    public static class Materials {

        @Schema(description = "文件ID")
        private String fileId;

        @Schema(description = "文件名称")
        private String fileName;

        @Schema(description = "文件大小")
        private Long fileSize;

        @Schema(description = "文件类型")
        private String fileType;

    }

}
