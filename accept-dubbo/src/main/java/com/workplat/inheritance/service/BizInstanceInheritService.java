package com.workplat.inheritance.service;

import com.workplat.gss.application.dubbo.vo.BizInstanceWorkFlowVO;
import com.workplat.gss.common.core.service.BaseService;
import com.workplat.inheritance.constant.InheritanceNodeStatusEnum;
import com.workplat.inheritance.constant.InheritanceStagePageEnum;
import com.workplat.inheritance.entity.BizInstanceInherit;

import java.util.List;

/**
 * 继承公证服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/8
 */
public interface BizInstanceInheritService extends BaseService<BizInstanceInherit> {


    /**
     * 根据instanceId获取
     *
     * @param instanceId
     * @return
     */
    BizInstanceInherit getByInstanceId(String instanceId);

    /**
     * 设置当前业务当前节点
     *
     * @param instanceId
     * @param inheritanceNodeStatus
     */
    void setInheritanceCurrentNode(String instanceId, InheritanceNodeStatusEnum inheritanceNodeStatus
            , String reviewOpinion);

    /**
     * 设置申报暂存当前停留界面
     *
     * @param instanceId
     * @param inheritanceStagePage
     */
    void setInheritanceStagePage(String instanceId, InheritanceStagePageEnum inheritanceStagePage);

    /**
     * 获取办件流程
     *
     * @param instanceId
     * @return
     */
    List<BizInstanceWorkFlowVO> getBizInstanceWorkFlowVOList(String instanceId);

}
