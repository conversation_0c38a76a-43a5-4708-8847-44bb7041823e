package com.workplat.inheritance.service;

import com.workplat.gss.common.core.service.BaseService;
import com.workplat.inheritance.entity.BizInstanceInheritSignature;
import com.workplat.inheritance.vo.AbandonDengBoInfoVO;
import com.workplat.inheritance.vo.NewPropertyOwnerInfoVO;

import java.util.List;

/**
 * 继承公证服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/21 15:39
 */
public interface BizInstanceInheritSignatureService extends BaseService<BizInstanceInheritSignature> {

    /**
     * 根据身份证查询签字列表
     *
     * @param zzqzrsfzhm 身份证
     * @return
     */
    List<BizInstanceInheritSignature> getSignatureInstanceByZzqzrsfzhm(String zzqzrsfzhm);

    /**
     * 查询签字列表
     *
     * @param instanceId 办件id
     * @return
     */
    List<BizInstanceInheritSignature> getBizInstanceInheritSignatureList(String instanceId);

    /**
     * 生成签名信息
     *
     * @param instanceId
     * @param newPropertyOwnerInfoVOList 新产权人信息列表
     * @param abandonDengBoInfoVOList    放弃登薄人信息列表
     */
    void generateSignatureInfo(String instanceId,
                               List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList, List<AbandonDengBoInfoVO> abandonDengBoInfoVOList);

    /**
     * 设置新产权人签字情况
     *
     * @param instanceId
     * @param newPropertyOwnerInfoVOList 新产权人信息列表
     * @param abandonDengBoInfoVOList    放弃登薄人信息列表
     */
    void setSignatureSituation(String instanceId,
                               List<NewPropertyOwnerInfoVO> newPropertyOwnerInfoVOList, List<AbandonDengBoInfoVO> abandonDengBoInfoVOList);

    /**
     * 重新签字
     *
     * @param instanceId
     * @param idNumber
     */
    void reSignature(String instanceId, String idNumber);

    /**
     * 签字提交
     *
     * @param instanceId
     * @param idNumber
     * @param file       base64编码
     */
    void signatureSubmit(String instanceId, String idNumber, String file) throws Exception;

}
