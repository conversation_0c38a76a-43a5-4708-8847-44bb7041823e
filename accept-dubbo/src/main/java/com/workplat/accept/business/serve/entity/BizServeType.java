package com.workplat.accept.business.serve.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @className: com.workplat.serve.entity
 * @description: 服务办理方式
 * @date 2025/5/14 14:07
 */
@Setter
@Getter
@Entity
@Table(name = "biz_serve_type")
public class BizServeType extends BaseEntity {


    @ManyToOne
    @JoinColumn(name = "method_id")
    private BizServeMethod method;
    /**
     * 数据字典:serve_method
     */
    @Comment("渠道类型")
    @Column(name = "blType", length = 32)
    private String type;

    @Comment("图标id")
    @Column(name = "icon", length = 32)
    private String icon;

    @Comment("渠道描述")
    @Column(name = "ms", length = 32)
    private String ms;

    @Comment("内容")
    @Column(name = "dz")
    private String dz;
}
