package com.workplat.accept.business.serve.vo;

import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @className: com.workplat.serve.entity
 * @description: 服务办理方式
 * @date 2025/5/14 14:07
 */
@Setter
@Getter

public class BizServeTypeVo extends BaseEntity {

    private String methodId;

    private String type;

    private String icon;

    private String ms;

    private String dz;

}
