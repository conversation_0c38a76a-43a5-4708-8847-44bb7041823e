package com.workplat.component.engine;

import com.workplat.component.dto.ComponentDataContext;

public interface ComponentEngineInterface {



    /**
     * 加载组件
     *
     * @return 加载结果
     */
    Object load(ComponentDataContext componentDataContext);

    /**
     * 数据处理
     *
     * @return 处理结果
     */
    Object process(ComponentDataContext componentDataContext);

    /**
     * 组件名称
     *
     * @return 组件名称
     */
    String name();

    /**
     * 组件编码
     *
     * @return 组件编码
     */
    String code();

}
